# 🏗️ 3. Architecture & Design Overview: <PERSON>zan

*   **Version:** 1.1
*   **Date:** September 19, 2025
*   **Status:** Revised
*   **Author:** <PERSON><PERSON><PERSON><PERSON> (Project Lead)
*   **Related Documents:** `1_Project_Vision_and_Scope.md`, `2_Software_Requirements_Specification.md`

---

## 1. High-Level Architecture

Mizan is designed as a **simple, decoupled system** optimized for free tier services. The architecture prioritizes operational simplicity and zero-cost operation during development and early deployment.

### 1.1 Architecture Diagram (Simplified)

```
                               +-------------------------------------------------------------+
                               |                        USER'S BROWSER                       |
                               |                                                             |
                               |  +-------------------------------------------------------+  |
                               |  |             MIZAN FRONTEND (Next.js App)              |  |
                               |  | (Hosted on Vercel Free Tier)                          |  |
                               |  +-------------------------------------------------------+  |
                               |                |                       ^                    |
                               |                | (1) API Calls         | (2) Page Renders   |
                               |                | (HTTPS/JSON)          | (HTML/CSS/JS)      |
                               +----------------|-----------------------|--------------------+
                                                v                       |
+-----------------------------------------------+-----------------------+-------------------------------------------------+
|                                                                                                                         |
|                                          AWS FREE TIER INFRASTRUCTURE                                                   |
|                                                                                                                         |
|  +---------------------------+                               +----------------------------+                             |
|  |                           |       (3) Database Queries    |                            |                             |
|  | EC2 t2.micro Instance     |-----------------------------> |  RDS PostgreSQL            |                             |
|  | (Docker Container)        |                               | (db.t3.micro - Free Tier)  |                             |
|  |                           |<--------------------------    |                            |                             |
|  | - NestJS Backend API      |       (4) Query Results       |                            |                             |
|  | - Meilisearch             |                               +----------------------------+                             |
|  | - Health Check Service    |                                                                                          |
|  |                           |                                                                                          |
|  | Port 3000: API            |       (5) File Operations     +----------------------------+                             |
|  | Port 7700: Meilisearch    |-----------------------------> |                            |                             |
|  |                           |                               |   CLOUDFLARE R2            |                             |
|  +---------------------------+<--------------------------    | (10GB Free Storage)        |                             |
|                                      (6) Presigned URLs      |                            |                             |
|                                                              +----------------------------+                             |
|                                                                                                                         |
|  +---------------------------+                               +----------------------------+                             |
|  |   GitHub Actions          |                               |   Free Monitoring          |                             |
|  | (CI/CD Pipeline)          |                               | - Sentry (Free Tier)       |                             |
|  | - Build Docker Image      |                               | - PostHog (Free Tier)      |                             |
|  | - Deploy to EC2           |                               | - UptimeRobot (Free)       |                             |
|  +---------------------------+                               +----------------------------+                             |
|                                                                                                                         |
|                                  +---------------------------------+                                                    |
|                                  |     ADMIN PANEL                 |                                                    |
|                                  | Retool (Free Tier - 5 users)    |                                                    |
|                                  +---------------------------------+                                                    |
|                                                                                                                         |
+-------------------------------------------------------------------------------------------------------------------------+
```

### 1.2 Deployment Strategy (Zero-Cost)

| Component               | Service                     | Free Tier Limits              | Our Usage                          |
| :---------------------- | :-------------------------- | :---------------------------- | :--------------------------------- |
| **Frontend**            | **Vercel Free**             | 100GB bandwidth/month         | ~20GB expected                    |
| **Backend + Search**    | **AWS EC2 t2.micro**        | 750 hours/month (1 year)      | 24/7 operation = 720 hours        |
| **Database**            | **AWS RDS db.t3.micro**     | 750 hours/month, 20GB storage | Single instance, 5GB needed       |
| **File Storage**        | **Cloudflare R2**           | 10GB storage, unlimited egress| ~8GB for initial content          |
| **Container Registry**  | **GitHub Container Registry** | 500MB storage               | ~200MB Docker image               |
| **Monitoring**          | **Sentry + PostHog**        | 5K errors, 1M events/month   | Well within limits                |

### 1.3 EC2 Instance Configuration

The entire backend runs on a single EC2 t2.micro instance using Docker Compose:

```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    image: ghcr.io/beabzk/mizan-backend:latest
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - R2_ACCESS_KEY=${R2_ACCESS_KEY}
      - MEILISEARCH_URL=http://meilisearch:7700
    depends_on:
      - meilisearch
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  meilisearch:
    image: getmeili/meilisearch:v1.9
    ports:
      - "7700:7700"
    volumes:
      - ./meili_data:/meili_data
    environment:
      - MEILI_MASTER_KEY=${MEILI_MASTER_KEY}
      - MEILI_ENV=production
    restart: unless-stopped

  # Simple health checker that reports to UptimeRobot
  health-check:
    image: alpine:latest
    command: sh -c "while true; do sleep 300; done"
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://backend:3000/health"]
      interval: 60s
```

---

## 2. Simplified Data Flows

### 2.1 User Authentication (Google OAuth)

1. User clicks "Sign In" → Vercel Frontend
2. Frontend redirects → Google OAuth
3. Google returns token → Frontend
4. Frontend sends token → EC2 Backend
5. Backend validates with Google → Creates session
6. Returns JWT → Frontend stores in cookie

### 2.2 File Upload (Admin Only)

1. Admin opens Retool panel
2. Selects files for bulk upload
3. Retool requests presigned URLs from EC2 Backend
4. Uploads directly to Cloudflare R2
5. Confirms upload to Backend
6. Backend stores metadata in RDS
7. Triggers Meilisearch indexing

### 2.3 Search & Download

1. User searches → Frontend → EC2 Backend
2. Backend queries Meilisearch (local)
3. Returns results to Frontend
4. User clicks download → Backend generates R2 presigned URL
5. Browser downloads directly from R2

---

## 3. Technology Stack (Optimized for Free Tier)

| Category                | Tool                        | Why This Choice                                                                                     |
| :---------------------- | :-------------------------- | :------------------------------------------------------------------------------------------------- |
| **Container Runtime**   | **Docker + Docker Compose** | Simple deployment, easy rollback, works great on single EC2 instance                              |
| **Process Manager**     | **PM2** (inside container)  | Handles process crashes, provides basic monitoring, zero config                                    |
| **Database ORM**        | **Prisma**                  | Type-safe, handles migrations, works perfectly with RDS                                           |
| **Rate Limiting**       | **express-rate-limit**      | Simple in-memory rate limiting, sufficient for single-instance deployment                          |
| **Session Storage**     | **In-memory** (for MVP)     | JWT-based auth means minimal session data, can add Redis later if needed                          |
| **Deployment**          | **GitHub Actions + SSH**    | Push to main → Build image → SSH to EC2 → Pull and restart container                              |
| **SSL/TLS**             | **Cloudflare (Free)**       | Point domain through Cloudflare for free SSL, DDoS protection, and basic analytics                |

### 3.1 Deployment Pipeline

```yaml
# .github/workflows/deploy.yml
name: Deploy to EC2

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Build and push Docker image
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin
          docker build -t ghcr.io/beabzk/mizan-backend:latest ./backend
          docker push ghcr.io/beabzk/mizan-backend:latest
      
      - name: Deploy to EC2
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ubuntu
          key: ${{ secrets.EC2_SSH_KEY }}
          script: |
            cd /home/<USER>/mizan
            docker-compose pull
            docker-compose up -d
            docker system prune -f
```

---

## 4. Scaling Path (Post-MVP)

When we outgrow the free tier, here's the migration path:

1. **Phase 1 (Current)**: Everything on single EC2 instance
2. **Phase 2 (1000+ users)**: Move Meilisearch to separate t2.small instance
3. **Phase 3 (5000+ users)**: Add Redis for caching, move to t3.small instances
4. **Phase 4 (10000+ users)**: Add load balancer, multiple backend instances

---

## 5. Monitoring & Operations

### 5.1 Health Checks

```typescript
// health.controller.ts
@Controller('health')
export class HealthController {
  @Get()
  check() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
  }

  @Get('deep')
  async deepCheck(
    @Inject('DATABASE') private db: PrismaService,
    @Inject('SEARCH') private search: MeilisearchService,
  ) {
    const checks = await Promise.allSettled([
      this.db.$queryRaw`SELECT 1`,
      this.search.health(),
    ]);
    
    return {
      database: checks[0].status === 'fulfilled' ? 'ok' : 'error',
      search: checks[1].status === 'fulfilled' ? 'ok' : 'error',
    };
  }
}
```

### 5.2 Backup Strategy

- **Database**: AWS RDS automated backups (7-day retention, free)
- **Meilisearch Data**: Daily cron job backs up to R2
- **Configuration**: All secrets in GitHub Secrets, infrastructure as code

---

## 6. Security Considerations

Without adding complexity or cost:

1. **Network Security**: 
   - Only ports 80/443 open to internet (via Cloudflare)
   - SSH restricted to your IP
   - Database only accessible from EC2

2. **Application Security**:
   - All secrets in environment variables
   - Rate limiting on all endpoints
   - Input validation with class-validator
   - Helmet.js for security headers

3. **Data Security**:
   - R2 bucket is private
   - All file access through presigned URLs
   - URLs expire after 5 minutes