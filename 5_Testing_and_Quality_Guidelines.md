# 🧪 5. Testing & Quality Guidelines: Mizan

*   **Version:** 1.0
*   **Date:** September 8, 2025
*   **Status:** Baseline
*   **Author:** be<PERSON><PERSON><PERSON> (Project Lead)
*   **Related Document:** `2_Software_Requirements_Specification.md`

---

## 1. Guiding Philosophy

Our approach to testing is pragmatic and risk-driven. We do not chase arbitrary coverage metrics. Instead, we write tests to gain **confidence** in our system's correctness and to **prevent future regressions**. A good test is one that is reliable, easy to understand, and fails for clear, specific reasons. All tests must be automated and integrated into our CI/CD pipeline.

## 2. Levels of Testing

We employ a standard testing pyramid strategy:

1.  **Unit Tests (High Volume):** These are fast, isolated tests that verify a single function or component. They form the foundation of our testing suite.
2.  **Integration Tests (Medium Volume):** These tests verify the interaction between multiple components, such as an API endpoint interacting with a database.
3.  **End-to-End (E2E) Tests (Low Volume, Post-MVP):** These tests simulate a full user journey in a real browser. They are powerful but slow and will be implemented after the initial launch.

## 3. Unit Testing Guidelines

### 3.1 Coverage Expectations

*   For the MVP, we will not enforce a strict, project-wide coverage percentage.
*   **Mandatory Coverage:** All critical business logic must be unit tested. This includes:
    *   Authentication and authorization services.
    *   Services that generate secure URLs or handle file metadata.
    *   Any complex data transformation or validation logic.
*   **Frameworks:** [**Jest**](https://jestjs.io/) will be used for both the frontend (with React Testing Library) and backend.

### 3.2 Naming Conventions

*   Test files must be co-located with the source file they are testing.
*   The file name must be `[filename].test.ts` (e.g., `file.service.ts` will have `file.service.test.ts`).

### 3.3 Example Test Case (Backend Service)

This example demonstrates the testing pattern for a critical function using the **Arrange-Act-Assert** structure.

**Function to Test:** `FileService.getDownloadUrl(user, fileId)`

```typescript
// file.service.test.ts

import { FileService } from './file.service';
import { PrismaService } from '../prisma/prisma.service'; // Mocked
import { R2Service } from '../r2/r2.service';         // Mocked
import { UnauthorizedException, NotFoundException } from '@nestjs/common';

describe('FileService', () => {
  let fileService: FileService;
  let mockPrisma: DeepMocked<PrismaService>;
  let mockR2: DeepMocked<R2Service>;

  // --- ARRANGE (Setup) ---
  beforeEach(() => {
    // Create mocks for all dependencies
    mockPrisma = createMock<PrismaService>();
    mockR2 = createMock<R2Service>();
    fileService = new FileService(mockPrisma, mockR2);
  });

  it('should return a secure download URL for a valid file request', async () => {
    // Arrange: Mock the database response and the R2 service response
    const mockFile = { id: 'file-uuid-123', storageKey: 'path/to/file.pdf', /* ... */ };
    const mockUser = { id: 'user-uuid-abc', role: 'USER' };
    const expectedUrl = 'https://r2.presigned.url/for/file.pdf';
    
    mockPrisma.file.findUnique.mockResolvedValue(mockFile);
    mockR2.getPresignedDownloadUrl.mockResolvedValue(expectedUrl);

    // --- ACT (Execute) ---
    const result = await fileService.getDownloadUrl(mockUser, mockFile.id);

    // --- ASSERT (Verify) ---
    expect(result).toEqual({ downloadUrl: expectedUrl });
    expect(mockPrisma.file.findUnique).toHaveBeenCalledWith({ where: { id: mockFile.id } });
    expect(mockR2.getPresignedDownloadUrl).toHaveBeenCalledWith(mockFile.storageKey);
  });

  it('should throw NotFoundException if the file does not exist', async () => {
    // Arrange: Mock the database to return null
    const mockUser = { id: 'user-uuid-abc', role: 'USER' };
    mockPrisma.file.findUnique.mockResolvedValue(null);

    // Act & Assert: Expect the function to reject with a specific error
    await expect(fileService.getDownloadUrl(mockUser, 'non-existent-uuid')).rejects.toThrow(NotFoundException);
  });
});
```

## 4. Integration Testing Guidelines

### 4.1 Scope

Integration tests will focus on the API endpoints. They will run against a real, separate test database to verify that the HTTP layer, business logic, and database queries work together correctly.

*   **Framework:** [**Supertest**](https://github.com/visionmedia/supertest) will be used in conjunction with Jest to make HTTP requests to the running NestJS application.

### 4.2 Example Test Case (API Endpoint)

**Endpoint to Test:** `GET /api/v1/courses/{courseId}/files`

```typescript
// course.e2e-spec.ts

import * as request from 'supertest';
import { Test } from '@nestjs/testing';
import { AppModule } from '../src/app.module';
import { INestApplication } from '@nestjs/common';

describe('Courses Controller (e2e)', () => {
  let app: INestApplication;
  let authToken: string; // Acquired from a login helper

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Helper function to login a test user and get a valid JWT
    authToken = await getAuthTokenForUser(app); 
  });

  it('GET /courses/{courseId}/files - should return files for a valid course', () => {
    const courseId = 'known-course-uuid'; // Seeded in test database

    return request(app.getHttpServer())
      .get(`/api/v1/courses/${courseId}/files`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200)
      .expect((res) => {
        expect(res.body.files).toBeInstanceOf(Array);
        expect(res.body.files.length).toBeGreaterThan(0);
        expect(res.body.pagination).toBeDefined();
      });
  });

  it('GET /courses/{courseId}/files - should return 401 Unauthorized without a token', () => {
    const courseId = 'known-course-uuid';

    return request(app.getHttpServer())
      .get(`/api/v1/courses/${courseId}/files`)
      .expect(401);
  });
});
```

## 5. Performance Benchmarks

Performance is a feature. All code must adhere to the benchmarks defined in the SRS (NFR-1).

*   **Server Response Time:** API endpoints must maintain a 95th percentile (p95) response time of **< 200ms** under normal load.
*   **Frontend Load Time:** Core pages must achieve a Largest Contentful Paint (LCP) of **< 2.5 seconds**.
*   **Measurement Tools:**
    *   Backend performance will be monitored via our observability tools.
    *   Frontend performance will be measured using [**Google Lighthouse**](https://developer.chrome.com/docs/lighthouse/overview/) and **Vercel Analytics**. Performance regressions identified in these tools must be addressed before merging to the main branch.
