# 🧭 1. Project Vision & Scope: Mizan

*   **Version:** 1.0
*   **Date:** September 8, 2025
*   **Status:** Baseline
*   **Author:** <PERSON><PERSON><PERSON><PERSON> (Project Lead)

_This document serves as the foundational charter for Project Mizan. It is the anchor for all strategic and development decisions, ensuring that every line of code serves our core purpose. It is a living document, but changes to its core tenets require careful consideration._

---

## Mission Statement

Mizan's mission is to bring balance to the academic lives of university students by transforming the chaotic, scattered landscape of digital course materials into a centralized, organized, and permanent repository of community-driven knowledge. We empower students to learn more effectively by making information accessible and searchable, while building a culture that recognizes and rewards valuable contributions.

## Target Audience / User Persona

Our primary user is **"<PERSON><PERSON><PERSON>"**:

*   **Demographics:** A 20-year-old, 2nd-year Computer Science student at a major Ethiopian university (e.g., AAU). He is highly proficient with smartphones and moderately proficient with laptops.
*   **Goals & Motivations:** <PERSON><PERSON><PERSON>'s primary goal is to achieve high grades to secure a good job after graduation. He is motivated by efficiency—finding the best study materials quickly—and by social proof and recognition from his peers.
*   **Frustrations (The "Before Mizan" State):**
    *   He belongs to 5-6 different Telegram groups for his courses, each flooded with duplicate files, outdated versions, and irrelevant chatter.
    *   Finding a specific past exam from two semesters ago requires him to scroll endlessly or ask classmates repeatedly.
    *   He has high-quality, well-organized notes but feels little incentive to share them in a forum where they will be quickly buried and uncredited.
    *   He wastes hours before exams just trying to collect and organize the correct versions of lecture slides and study guides.

## Core Problem Being Solved

Mizan directly addresses the failures of using ephemeral messaging apps for managing critical academic knowledge. The specific problems are:

*   **Information Chaos:** Essential files are disorganized, duplicated, and mixed with casual conversation, making them difficult to find.
*   **Lack of Persistence:** Valuable knowledge is lost over time as students graduate and groups become inactive. There is no institutional memory.
*   **Zero Searchability:** It is impossible to search for specific content within documents or to filter resources effectively.
*   **No Quality Control:** There is no mechanism to identify the best, most helpful, or most recent versions of a document.
*   **Missing Incentives:** There is no system to recognize or reward the students who contribute the highest quality materials, leading to low participation.

## Out-of-Scope for the Initial MVP (The "Not Now" List)

To ensure a focused and rapid launch, the following features and functionalities are explicitly **out of scope** for the initial MVP build. This list is our primary defense against feature creep.

*   **User-Generated Content:** Users cannot upload, edit, or delete files. All initial content will be seeded by the admin team.
*   **Gamification & Points System:** There will be no points, badges, or leaderboard in the first version.
*   **Social Features:** No comments, ratings, user-to-user messaging, or public user profiles (beyond a name).
*   **Premium Content & Monetization:** All content will be free. There will be no OCR service, paywalls, or "premium" features.
*   **Advanced Search Features:** Search will be functional but will not include faceted filtering (e.g., filter by year AND file type).
*   **User-Managed Profiles:** Users can sign in with Google, but there will be no functionality to edit profiles, change usernames, or upload avatars.
*   **Direct Mobile App:** Mizan will be a responsive web application. A native app or a Progressive Web App (PWA) with offline capabilities is not part of the MVP.
*   **Automated Content Moderation:** Moderation will be a manual process handled via the Admin Panel.
*   **Multi-Language Support in UI:** The user interface will be in English only for the MVP.