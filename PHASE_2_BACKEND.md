# 🔧 Phase 2: Backend Core Development (Week 2)

**Objective:** Build the complete NestJS backend with authentication, database integration, and core APIs.

**Duration:** 7 days  
**Prerequisites:** Phase 1 completed, Node.js knowledge, TypeScript familiarity

---

## 📋 Overview

This phase creates the complete backend API for Mizan including:
- NestJS application with TypeScript
- Prisma ORM with PostgreSQL integration
- Better Auth with Google OAuth
- Complete API endpoints for hierarchy, files, and search
- Health monitoring and Docker containerization

**Technologies:**
- NestJS (TypeScript framework)
- Prisma (Database ORM)
- Better Auth (Authentication)
- PostgreSQL (Database)
- Docker (Containerization)

---

## 🚀 Day 1: Project Initialization

### Step 1: Setup Development Environment

```bash
# 1. SSH into your EC2 instance
ssh -i mizan-key.pem ubuntu@<EC2_PUBLIC_IP>

# 2. Install Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 3. Verify installation
node --version  # Should be v18.x.x
npm --version   # Should be 9.x.x

# 4. Install global packages
sudo npm install -g @nestjs/cli pm2

# 5. Create project directory
mkdir -p ~/mizan/backend
cd ~/mizan/backend
```

### Step 2: Initialize NestJS Project

```bash
# 1. Create NestJS application
nest new mizan-backend --package-manager npm
cd mizan-backend

# 2. Install core dependencies
npm install @prisma/client prisma
npm install @better-auth/nestjs @better-auth/prisma-adapter
npm install @nestjs/config @nestjs/jwt @nestjs/passport
npm install passport passport-google-oauth20
npm install express-rate-limit helmet compression
npm install class-validator class-transformer
npm install @aws-sdk/client-s3 @aws-sdk/s3-request-presigner

# 3. Install development dependencies
npm install -D @types/passport-google-oauth20
npm install -D @types/multer
npm install -D jest @nestjs/testing supertest
npm install -D @types/jest @types/supertest
```

### Step 3: Project Structure Setup

```bash
# 1. Create module directories
mkdir -p src/{auth,user,hierarchy,files,search,health,common}
mkdir -p src/common/{guards,decorators,filters,interceptors}

# 2. Create configuration directory
mkdir -p src/config

# 3. Create Prisma directory
mkdir -p prisma

# 4. View current structure
tree src/
```

---

## 🗄️ Day 2: Database Schema Implementation

### Step 1: Initialize Prisma

```bash
# 1. Initialize Prisma
npx prisma init

# 2. Configure environment variables
cat > .env << 'EOF'
# Database
DATABASE_URL="**************************************************************/mizan?schema=public"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="7d"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_CALLBACK_URL="http://<EC2_PUBLIC_IP>/api/auth/google/callback"

# R2 Storage
R2_ACCOUNT_ID="your-r2-account-id"
R2_ACCESS_KEY_ID="your-r2-access-key"
R2_SECRET_ACCESS_KEY="your-r2-secret-key"
R2_BUCKET_NAME="mizan-files"
R2_ENDPOINT="https://<ACCOUNT_ID>.r2.cloudflarestorage.com"

# Server
PORT=3000
NODE_ENV=development

# Meilisearch (for Phase 4)
MEILISEARCH_URL="http://localhost:7700"
MEILISEARCH_MASTER_KEY="your-meili-master-key-here"
EOF

# 3. Add .env to .gitignore
echo ".env" >> .gitignore
```

### Step 2: Define Prisma Schema

```bash
# 1. Replace prisma/schema.prisma with complete schema
cat > prisma/schema.prisma << 'EOF'
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// --- Core Content Hierarchy ---
model University {
  id          String       @id @default(uuid())
  name        String       @unique
  slug        String       @unique
  departments Department[]
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

model Department {
  id           String     @id @default(uuid())
  name         String
  slug         String
  universityId String
  university   University @relation(fields: [universityId], references: [id], onDelete: Cascade)
  courses      Course[]
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  
  @@unique([slug, universityId])
}

model Course {
  id           String      @id @default(uuid())
  name         String
  courseCode   String
  slug         String
  departmentId String
  department   Department  @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  files        FileCourse[]
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  
  @@unique([courseCode, departmentId])
}

// --- File & Content ---
model File {
  id            String       @id @default(uuid())
  fileName      String
  originalName  String
  fileType      String
  extension     String
  fileSize      Int
  storageKey    String       @unique
  fileHash      String       @unique
  
  // Metadata
  title         String
  description   String?
  academicYear  Int
  semester      Int
  category      FileCategory
  
  // Relations
  courses       FileCourse[]
  tags          FileTag[]
  uploaderId    String?
  uploader      User?        @relation(fields: [uploaderId], references: [id], onDelete: SetNull)
  
  // Stats
  downloadCount Int          @default(0)
  viewCount     Int          @default(0)
  
  // Timestamps
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  deletedAt     DateTime?
  
  @@index([fileHash])
  @@index([academicYear, semester])
  @@index([category])
}

// Join table for many-to-many File <-> Course
model FileCourse {
  file      File     @relation(fields: [fileId], references: [id], onDelete: Cascade)
  fileId    String
  course    Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId  String
  isPrimary Boolean  @default(false)
  
  @@id([fileId, courseId])
}

model Tag {
  id    String    @id @default(uuid())
  name  String    @unique
  slug  String    @unique
  files FileTag[]
  
  createdAt DateTime @default(now())
}

// Join table for many-to-many File <-> Tag
model FileTag {
  file   File   @relation(fields: [fileId], references: [id], onDelete: Cascade)
  fileId String
  tag    Tag    @relation(fields: [tagId], references: [id], onDelete: Cascade)
  tagId  String
  
  @@id([fileId, tagId])
}

// --- User & Auth ---
model User {
  id            String    @id @default(uuid())
  email         String    @unique
  name          String
  googleId      String    @unique
  avatarUrl     String?
  role          UserRole  @default(USER)
  
  // Activity tracking
  lastLoginAt   DateTime?
  loginCount    Int       @default(0)
  
  // Relations
  uploadedFiles File[]
  downloads     Download[]
  searchQueries SearchQuery[]
  
  // Timestamps
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  deletedAt     DateTime?
  
  @@index([email])
  @@index([googleId])
}

// --- Analytics & Tracking ---
model Download {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  fileId    String
  createdAt DateTime @default(now())
  
  @@index([userId, fileId])
  @@index([createdAt])
}

model SearchQuery {
  id          String   @id @default(uuid())
  query       String
  resultsCount Int
  userId      String?
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  createdAt   DateTime @default(now())
  
  @@index([query])
  @@index([createdAt])
}

// --- Enums ---
enum UserRole {
  USER
  ADMIN
}

enum FileCategory {
  EXAM
  LECTURE_NOTE
  ASSIGNMENT
  LAB_MANUAL
  PROJECT
  TEXTBOOK
  REFERENCE
  OTHER
}
EOF
```

### Step 3: Generate and Deploy Schema

```bash
# 1. Generate Prisma client
npx prisma generate

# 2. Push schema to database
npx prisma db push

# 3. Verify database connection
npx prisma studio --port 5555
# Access at http://<EC2_PUBLIC_IP>:5555 (optional)
# Press Ctrl+C to stop
```

---

## 🔐 Day 2-3: Authentication Implementation

### Step 1: Configuration Module

```bash
# 1. Create configuration service
cat > src/config/configuration.ts << 'EOF'
export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  database: {
    url: process.env.DATABASE_URL,
  },
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  },
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackUrl: process.env.GOOGLE_CALLBACK_URL,
  },
  r2: {
    accountId: process.env.R2_ACCOUNT_ID,
    accessKeyId: process.env.R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
    bucketName: process.env.R2_BUCKET_NAME,
    endpoint: process.env.R2_ENDPOINT,
  },
});
EOF
```

### Step 2: Prisma Service

```bash
# 1. Create Prisma service
cat > src/prisma.service.ts << 'EOF'
import { Injectable, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  async onModuleInit() {
    await this.$connect();
  }
}
EOF
```

### Step 3: Auth Module

```bash
# 1. Generate auth module
nest generate module auth
nest generate service auth
nest generate controller auth

# 2. Create auth DTOs
mkdir -p src/auth/dto
cat > src/auth/dto/auth.dto.ts << 'EOF'
import { IsString, IsNotEmpty } from 'class-validator';

export class GoogleAuthDto {
  @IsString()
  @IsNotEmpty()
  code: string;
}

export class AuthResponseDto {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  user: {
    id: string;
    name: string;
    email: string;
    avatarUrl?: string;
    role: string;
  };
}
EOF
```

### Step 4: Google OAuth Strategy

```bash
# 1. Create Google strategy
cat > src/auth/google.strategy.ts << 'EOF'
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(private configService: ConfigService) {
    super({
      clientID: configService.get('google.clientId'),
      clientSecret: configService.get('google.clientSecret'),
      callbackURL: configService.get('google.callbackUrl'),
      scope: ['email', 'profile'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    const { id, name, emails, photos } = profile;
    const user = {
      googleId: id,
      email: emails[0].value,
      name: `${name.givenName} ${name.familyName}`,
      avatarUrl: photos[0].value,
    };
    done(null, user);
  }
}
EOF
```

---

## 🔧 Day 3-4: Core API Endpoints

### Step 1: User Module

```bash
# 1. Generate user module
nest generate module user
nest generate service user

# 2. Create user service
cat > src/user/user.service.ts << 'EOF'
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { User, UserRole } from '@prisma/client';

@Injectable()
export class UserService {
  constructor(private prisma: PrismaService) {}

  async findByGoogleId(googleId: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { googleId },
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  async create(userData: {
    googleId: string;
    email: string;
    name: string;
    avatarUrl?: string;
  }): Promise<User> {
    return this.prisma.user.create({
      data: {
        ...userData,
        role: UserRole.USER,
        loginCount: 1,
        lastLoginAt: new Date(),
      },
    });
  }

  async updateLoginInfo(userId: string): Promise<User> {
    return this.prisma.user.update({
      where: { id: userId },
      data: {
        loginCount: { increment: 1 },
        lastLoginAt: new Date(),
      },
    });
  }
}
EOF
```

### Step 2: Hierarchy Module

```bash
# 1. Generate hierarchy module
nest generate module hierarchy
nest generate service hierarchy
nest generate controller hierarchy

# 2. Create hierarchy service
cat > src/hierarchy/hierarchy.service.ts << 'EOF'
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';

@Injectable()
export class HierarchyService {
  constructor(private prisma: PrismaService) {}

  async getUniversities() {
    return this.prisma.university.findMany({
      include: {
        _count: {
          select: { departments: true },
        },
      },
      orderBy: { name: 'asc' },
    });
  }

  async getDepartmentsByUniversity(universitySlug: string) {
    const university = await this.prisma.university.findUnique({
      where: { slug: universitySlug },
      include: {
        departments: {
          include: {
            _count: {
              select: { courses: true },
            },
          },
          orderBy: { name: 'asc' },
        },
      },
    });

    if (!university) {
      throw new Error('University not found');
    }

    return university.departments;
  }

  async getCoursesByDepartment(departmentSlug: string, universitySlug: string) {
    const department = await this.prisma.department.findFirst({
      where: {
        slug: departmentSlug,
        university: { slug: universitySlug },
      },
      include: {
        courses: {
          include: {
            _count: {
              select: { files: true },
            },
          },
          orderBy: { name: 'asc' },
        },
      },
    });

    if (!department) {
      throw new Error('Department not found');
    }

    return department.courses;
  }
}
EOF
```

### Step 3: Files Module

```bash
# 1. Generate files module
nest generate module files
nest generate service files
nest generate controller files

# 2. Create R2 service for file storage
cat > src/files/r2.service.ts << 'EOF'
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

@Injectable()
export class R2Service {
  private s3Client: S3Client;
  private bucketName: string;

  constructor(private configService: ConfigService) {
    this.s3Client = new S3Client({
      region: 'auto',
      endpoint: this.configService.get('r2.endpoint'),
      credentials: {
        accessKeyId: this.configService.get('r2.accessKeyId'),
        secretAccessKey: this.configService.get('r2.secretAccessKey'),
      },
    });
    this.bucketName = this.configService.get('r2.bucketName');
  }

  async getPresignedDownloadUrl(storageKey: string): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.bucketName,
      Key: storageKey,
    });

    return getSignedUrl(this.s3Client, command, { expiresIn: 300 }); // 5 minutes
  }
}
EOF
```

---

## 🏥 Day 4-5: Health Checks and Monitoring

### Step 1: Health Module

```bash
# 1. Install Terminus for health checks
npm install @nestjs/terminus

# 2. Generate health module
nest generate module health
nest generate controller health

# 3. Create health controller
cat > src/health/health.controller.ts << 'EOF'
import { Controller, Get } from '@nestjs/common';
import { HealthCheck, HealthCheckService, PrismaHealthIndicator } from '@nestjs/terminus';
import { PrismaService } from '../prisma.service';

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private prismaHealth: PrismaHealthIndicator,
    private prisma: PrismaService,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0',
    };
  }

  @Get('detailed')
  @HealthCheck()
  checkDetailed() {
    return this.health.check([
      () => this.prismaHealth.pingCheck('database', this.prisma),
    ]);
  }
}
EOF
```

### Step 2: Global Configuration

```bash
# 1. Update main.ts with global configuration
cat > src/main.ts << 'EOF'
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import * as compression from 'compression';
import rateLimit from 'express-rate-limit';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Security
  app.use(helmet());
  app.use(compression());

  // Rate limiting
  app.use(
    rateLimit({
      windowMs: 1000, // 1 second
      max: 10, // limit each IP to 10 requests per windowMs
      message: 'Too many requests from this IP',
    }),
  );

  // CORS
  app.enableCors({
    origin: process.env.NODE_ENV === 'production' 
      ? ['https://mizan.vercel.app'] // Update with your frontend URL
      : true,
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Global prefix
  app.setGlobalPrefix('api');

  const port = configService.get('port');
  await app.listen(port);
  console.log(`🚀 Application is running on: http://localhost:${port}/api`);
}
bootstrap();
EOF
```

---

## 🐳 Day 5-6: Dockerization

### Step 1: Create Dockerfile

```bash
# 1. Create Dockerfile
cat > Dockerfile << 'EOF'
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY prisma ./prisma/

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/prisma ./prisma

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Change ownership
RUN chown -R nestjs:nodejs /app
USER nestjs

EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

CMD ["node", "dist/main"]
EOF
```

### Step 2: Create Docker Compose

```bash
# 1. Create docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  backend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_CALLBACK_URL=${GOOGLE_CALLBACK_URL}
      - R2_ACCOUNT_ID=${R2_ACCOUNT_ID}
      - R2_ACCESS_KEY_ID=${R2_ACCESS_KEY_ID}
      - R2_SECRET_ACCESS_KEY=${R2_SECRET_ACCESS_KEY}
      - R2_BUCKET_NAME=${R2_BUCKET_NAME}
      - R2_ENDPOINT=${R2_ENDPOINT}
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Meilisearch will be added in Phase 4
  # meilisearch:
  #   image: getmeili/meilisearch:v1.9
  #   ports:
  #     - "7700:7700"
  #   volumes:
  #     - ./meili_data:/meili_data
  #   environment:
  #     - MEILI_MASTER_KEY=${MEILISEARCH_MASTER_KEY}
  #     - MEILI_ENV=production
  #   restart: unless-stopped
EOF
```

### Step 3: Build and Test

```bash
# 1. Build Docker image
docker-compose build

# 2. Test local run
docker-compose up -d

# 3. Check logs
docker-compose logs -f backend

# 4. Test health endpoint
curl http://localhost:3000/api/health

# 5. Stop containers
docker-compose down
```

---

## ✅ Phase 2 Completion Checklist

### Backend Core ✅
- [ ] NestJS application created and configured
- [ ] All required dependencies installed
- [ ] TypeScript configuration optimized
- [ ] Global pipes and middleware configured

### Database ✅
- [ ] Prisma schema defined with all models
- [ ] Database connection established
- [ ] Prisma client generated and working
- [ ] Database migrations applied

### Authentication ✅
- [ ] Google OAuth strategy implemented
- [ ] JWT token generation working
- [ ] User creation and login flow complete
- [ ] Authentication guards created

### API Endpoints ✅
- [ ] Hierarchy endpoints (universities, departments, courses)
- [ ] File management endpoints
- [ ] User management endpoints
- [ ] Health check endpoints

### Security ✅
- [ ] Rate limiting configured
- [ ] Security headers (Helmet) applied
- [ ] Input validation with class-validator
- [ ] CORS properly configured

### Containerization ✅
- [ ] Dockerfile created and optimized
- [ ] Docker Compose configuration ready
- [ ] Health checks implemented
- [ ] Environment variables properly configured

---

## 🧪 Testing Your Backend

```bash
# 1. Test health endpoint
curl http://<EC2_PUBLIC_IP>:3000/api/health

# 2. Test universities endpoint (will be empty until seeded)
curl http://<EC2_PUBLIC_IP>:3000/api/universities

# 3. Check Docker container status
docker-compose ps

# 4. View application logs
docker-compose logs backend
```

---

## 🚀 Next Steps

Your backend is now complete! Proceed to **[Phase 3: Frontend Development](./PHASE_3_FRONTEND.md)** to build the Next.js frontend.

**What you have:**
- ✅ Complete NestJS backend with TypeScript
- ✅ Database integration with Prisma ORM
- ✅ Google OAuth authentication system
- ✅ All core API endpoints implemented
- ✅ Docker containerization ready
- ✅ Health monitoring and security configured

**API Base URL:** `http://<EC2_PUBLIC_IP>:3000/api`
