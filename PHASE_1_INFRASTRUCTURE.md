# 🏗️ Phase 1: Infrastructure Setup (Week 1)

**Objective:** Set up AWS infrastructure, database, storage, and basic security without requiring a custom domain.

**Duration:** 7 days  
**Prerequisites:** GitHub Student Pack, AWS account, basic Linux knowledge

---

## 📋 Overview

This phase establishes the foundational infrastructure for <PERSON>zan using AWS Free Tier services. We'll set up:
- EC2 instance for backend hosting
- RDS PostgreSQL database
- Cloudflare R2 for file storage
- Basic security and SSL configuration
- Docker environment for containerized deployment

**Cost:** $0 (using free tier services)

---

## 🎯 Day 1-2: AWS Account and Core Services

### Step 1: AWS Account Setup

```bash
# 1. Apply for GitHub Student Pack (if not already done)
# Visit: https://education.github.com/pack
# This provides $100+ in AWS credits

# 2. Create AWS account or sign in
# Visit: https://aws.amazon.com/
# Use the same email as your GitHub account for student benefits

# 3. Activate AWS Educate credits
# Visit: https://aws.amazon.com/education/awseducate/
# Link your GitHub Student Pack benefits
```

### Step 2: IAM User Creation

```bash
# 1. Create IAM user for programmatic access
aws iam create-user --user-name mizan-admin

# 2. Attach necessary policies
aws iam attach-user-policy \
  --user-name mizan-admin \
  --policy-arn arn:aws:iam::aws:policy/PowerUserAccess

# 3. Create access keys
aws iam create-access-key --user-name mizan-admin

# 4. Save the output securely - you'll need these credentials
# Access Key ID: AKIA...
# Secret Access Key: ...
```

### Step 3: AWS CLI Configuration

```bash
# 1. Install AWS CLI (if not installed)
# Windows: Download from https://aws.amazon.com/cli/
# macOS: brew install awscli
# Linux: sudo apt install awscli

# 2. Configure AWS CLI
aws configure
# AWS Access Key ID: [Enter your access key]
# AWS Secret Access Key: [Enter your secret key]
# Default region name: us-east-1
# Default output format: json

# 3. Test configuration
aws sts get-caller-identity
```

---

## 🖥️ Day 2-3: EC2 Instance Setup

### Step 1: Create Security Group

```bash
# 1. Create security group for backend
aws ec2 create-security-group \
  --group-name mizan-backend-sg \
  --description "Security group for Mizan backend server"

# 2. Get the security group ID from output
# sg-xxxxxxxxx

# 3. Add inbound rules
# SSH access (restrict to your IP in production)
aws ec2 authorize-security-group-ingress \
  --group-id sg-xxxxxxxxx \
  --protocol tcp \
  --port 22 \
  --cidr 0.0.0.0/0

# HTTP access
aws ec2 authorize-security-group-ingress \
  --group-id sg-xxxxxxxxx \
  --protocol tcp \
  --port 80 \
  --cidr 0.0.0.0/0

# HTTPS access
aws ec2 authorize-security-group-ingress \
  --group-id sg-xxxxxxxxx \
  --protocol tcp \
  --port 443 \
  --cidr 0.0.0.0/0

# Backend API port
aws ec2 authorize-security-group-ingress \
  --group-id sg-xxxxxxxxx \
  --protocol tcp \
  --port 3000 \
  --cidr 0.0.0.0/0
```

### Step 2: Create Key Pair

```bash
# 1. Create EC2 key pair
aws ec2 create-key-pair \
  --key-name mizan-key \
  --query 'KeyMaterial' \
  --output text > mizan-key.pem

# 2. Set proper permissions
chmod 400 mizan-key.pem

# 3. Store key securely - you'll need it to access your server
```

### Step 3: Launch EC2 Instance

```bash
# 1. Find Ubuntu 22.04 AMI ID for your region
aws ec2 describe-images \
  --owners 099720109477 \
  --filters "Name=name,Values=ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*" \
  --query 'Images[*].[ImageId,Name,CreationDate]' \
  --output table

# 2. Launch t2.micro instance (free tier eligible)
aws ec2 run-instances \
  --image-id ami-0c02fb55956c7d316 \
  --count 1 \
  --instance-type t2.micro \
  --key-name mizan-key \
  --security-group-ids sg-xxxxxxxxx \
  --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=mizan-backend}]'

# 3. Get instance details
aws ec2 describe-instances \
  --filters "Name=tag:Name,Values=mizan-backend" \
  --query 'Reservations[*].Instances[*].[InstanceId,PublicIpAddress,State.Name]' \
  --output table

# 4. Wait for instance to be running (2-3 minutes)
aws ec2 wait instance-running --instance-ids i-xxxxxxxxx
```

### Step 4: Initial Server Configuration

```bash
# 1. SSH into your instance
ssh -i mizan-key.pem ubuntu@<EC2_PUBLIC_IP>

# 2. Update system packages
sudo apt update && sudo apt upgrade -y

# 3. Install essential packages
sudo apt install -y curl wget git unzip htop nano

# 4. Configure timezone (optional)
sudo timedatectl set-timezone UTC

# 5. Create application directory
mkdir -p ~/mizan
cd ~/mizan

# 6. Set up basic firewall (optional but recommended)
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw allow 3000
sudo ufw --force enable
```

---

## 🐳 Day 3-4: Docker Installation

### Step 1: Install Docker

```bash
# 1. Remove any old Docker installations
sudo apt remove docker docker-engine docker.io containerd runc

# 2. Update package index and install dependencies
sudo apt update
sudo apt install -y \
  ca-certificates \
  curl \
  gnupg \
  lsb-release

# 3. Add Docker's official GPG key
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# 4. Set up Docker repository
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 5. Install Docker Engine
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 6. Add user to docker group
sudo usermod -aG docker ubuntu

# 7. Enable Docker to start on boot
sudo systemctl enable docker
sudo systemctl start docker
```

### Step 2: Install Docker Compose

```bash
# 1. Download Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 2. Make it executable
sudo chmod +x /usr/local/bin/docker-compose

# 3. Create symlink for easier access
sudo ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose

# 4. Logout and login again for group changes
exit
ssh -i mizan-key.pem ubuntu@<EC2_PUBLIC_IP>

# 5. Verify installation
docker --version
docker-compose --version
docker run hello-world
```

---

## 🗄️ Day 4-5: Database Setup

### Step 1: Create RDS Subnet Group

```bash
# 1. Get default VPC ID
aws ec2 describe-vpcs --filters "Name=isDefault,Values=true" --query 'Vpcs[0].VpcId' --output text

# 2. Get subnet IDs for the VPC
aws ec2 describe-subnets --filters "Name=vpc-id,Values=vpc-xxxxxxxxx" --query 'Subnets[*].SubnetId' --output text

# 3. Create DB subnet group (if using custom VPC)
aws rds create-db-subnet-group \
  --db-subnet-group-name mizan-db-subnet-group \
  --db-subnet-group-description "Subnet group for Mizan database" \
  --subnet-ids subnet-xxxxxxxxx subnet-yyyyyyyyy
```

### Step 2: Create Database Security Group

```bash
# 1. Create security group for database
aws ec2 create-security-group \
  --group-name mizan-db-sg \
  --description "Security group for Mizan PostgreSQL database"

# 2. Allow PostgreSQL access from backend security group
aws ec2 authorize-security-group-ingress \
  --group-id sg-db-xxxxxxxxx \
  --protocol tcp \
  --port 5432 \
  --source-group sg-backend-xxxxxxxxx
```

### Step 3: Create RDS PostgreSQL Instance

```bash
# 1. Create PostgreSQL database instance
aws rds create-db-instance \
  --db-instance-identifier mizan-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --engine-version 15.4 \
  --master-username mizanadmin \
  --master-user-password 'SecurePassword123!' \
  --allocated-storage 20 \
  --storage-type gp2 \
  --vpc-security-group-ids sg-db-xxxxxxxxx \
  --backup-retention-period 7 \
  --storage-encrypted \
  --publicly-accessible

# 2. Wait for database to be available (10-15 minutes)
aws rds wait db-instance-available --db-instance-identifier mizan-db

# 3. Get database endpoint
aws rds describe-db-instances \
  --db-instance-identifier mizan-db \
  --query 'DBInstances[0].Endpoint.Address' \
  --output text

# Save this endpoint - you'll need it for backend configuration
# Example: mizan-db.xxxxxxxxx.us-east-1.rds.amazonaws.com
```

### Step 4: Test Database Connection

```bash
# 1. Install PostgreSQL client on EC2
sudo apt install -y postgresql-client

# 2. Test connection to RDS
psql -h <RDS_ENDPOINT> -U mizanadmin -d postgres
# Enter password when prompted: SecurePassword123!

# 3. Create application database
CREATE DATABASE mizan;
\l
\q

# Connection successful! Database is ready for use.
```

---

## ☁️ Day 5-6: Cloudflare R2 Storage Setup

### Step 1: Create Cloudflare Account

```bash
# 1. Sign up for Cloudflare account
# Visit: https://dash.cloudflare.com/sign-up
# Use your primary email address

# 2. Navigate to R2 Object Storage
# Go to: https://dash.cloudflare.com/
# Click on "R2 Object Storage" in the sidebar
# Click "Purchase R2" (it's free for 10GB)
```

### Step 2: Create R2 Bucket

```bash
# 1. Create bucket for file storage
# In Cloudflare dashboard:
# - Click "Create bucket"
# - Bucket name: "mizan-files"
# - Location: Automatic (recommended)
# - Click "Create bucket"

# 2. Configure bucket settings
# - Enable public access: NO (keep private)
# - Configure CORS if needed later
```

### Step 3: Generate R2 API Tokens

```bash
# 1. Create R2 API token
# Go to: My Profile > API Tokens
# Click "Create Token"
# Use "Custom token" template

# 2. Configure token permissions
# Account: Your account
# Zone Resources: All zones
# Account Resources: Cloudflare R2:Edit

# 3. Save token details securely
# Account ID: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# Access Key ID: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# Secret Access Key: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### Step 4: Test R2 Connection

```bash
# 1. Configure AWS CLI for R2 (S3-compatible)
aws configure set aws_access_key_id <R2_ACCESS_KEY_ID>
aws configure set aws_secret_access_key <R2_SECRET_ACCESS_KEY>

# 2. Test bucket access
aws s3 ls --endpoint-url https://<ACCOUNT_ID>.r2.cloudflarestorage.com

# 3. Test file upload
echo "Test file for Mizan" > test.txt
aws s3 cp test.txt s3://mizan-files/ --endpoint-url https://<ACCOUNT_ID>.r2.cloudflarestorage.com

# 4. Verify upload
aws s3 ls s3://mizan-files/ --endpoint-url https://<ACCOUNT_ID>.r2.cloudflarestorage.com

# 5. Clean up test file
rm test.txt
aws s3 rm s3://mizan-files/test.txt --endpoint-url https://<ACCOUNT_ID>.r2.cloudflarestorage.com
```

---

## 🔒 Day 6-7: SSL and Security Configuration

### Step 1: Install Nginx

```bash
# 1. Install Nginx
sudo apt update
sudo apt install -y nginx

# 2. Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 3. Test Nginx installation
curl http://localhost
# Should return Nginx welcome page
```

### Step 2: Configure Nginx as Reverse Proxy

```bash
# 1. Create Nginx configuration for Mizan
sudo nano /etc/nginx/sites-available/mizan

# Add the following configuration:
```

```nginx
server {
    listen 80;
    server_name <EC2_PUBLIC_IP>;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # API routes
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Health check
    location /health {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Default location
    location / {
        return 200 'Mizan API Server';
        add_header Content-Type text/plain;
    }
}
```

### Step 3: Enable Nginx Configuration

```bash
# 1. Enable the site
sudo ln -s /etc/nginx/sites-available/mizan /etc/nginx/sites-enabled/

# 2. Remove default site
sudo rm /etc/nginx/sites-enabled/default

# 3. Test configuration
sudo nginx -t

# 4. Reload Nginx
sudo systemctl reload nginx

# 5. Test external access
curl http://<EC2_PUBLIC_IP>
# Should return "Mizan API Server"
```

### Step 4: SSL Certificate Setup (Optional)

```bash
# Option 1: Self-signed certificate for development
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/ssl/private/mizan-selfsigned.key \
  -out /etc/ssl/certs/mizan-selfsigned.crt

# Option 2: Let's Encrypt (requires domain)
# sudo apt install certbot python3-certbot-nginx
# sudo certbot --nginx -d yourdomain.com

# For now, we'll use HTTP only since no domain is available
```

---

## ✅ Phase 1 Completion Checklist

### Infrastructure ✅
- [ ] AWS account set up with free tier monitoring
- [ ] IAM user created with appropriate permissions
- [ ] EC2 t2.micro instance running Ubuntu 22.04
- [ ] Security groups configured for backend and database
- [ ] SSH key pair created and secured

### Services ✅
- [ ] Docker and Docker Compose installed and working
- [ ] RDS PostgreSQL database created and accessible
- [ ] Database connection tested successfully
- [ ] Cloudflare R2 bucket created and configured
- [ ] R2 API tokens generated and tested

### Security ✅
- [ ] Nginx installed and configured as reverse proxy
- [ ] Basic security headers configured
- [ ] Firewall rules configured (UFW)
- [ ] SSH access secured with key-based authentication
- [ ] Database access restricted to backend security group

### Environment Variables ✅
Create a `.env` file with the following variables for Phase 2:

```bash
# Database
DATABASE_URL="**************************************************************/mizan?schema=public"

# R2 Storage
R2_ACCOUNT_ID="your-r2-account-id"
R2_ACCESS_KEY_ID="your-r2-access-key"
R2_SECRET_ACCESS_KEY="your-r2-secret-key"
R2_BUCKET_NAME="mizan-files"
R2_ENDPOINT="https://<ACCOUNT_ID>.r2.cloudflarestorage.com"

# Server
SERVER_URL="http://<EC2_PUBLIC_IP>"
PORT=3000

# Meilisearch (for Phase 4)
MEILISEARCH_URL="http://localhost:7700"
MEILISEARCH_MASTER_KEY="your-meili-master-key-here"
```

---

## 🚀 Next Steps

Your infrastructure is now ready! Proceed to **[Phase 2: Backend Development](./PHASE_2_BACKEND.md)** to start building the NestJS application.

**What you have:**
- ✅ EC2 server ready for deployment
- ✅ PostgreSQL database ready for connections
- ✅ R2 storage ready for file uploads
- ✅ Nginx configured for API routing
- ✅ Basic security measures in place

**Estimated costs so far:** $0 (all free tier services)
