# 🎨 Phase 3: Frontend Foundation (Week 3)

**Objective:** Build a complete Next.js frontend with authentication, navigation, and file management.

**Duration:** 7 days  
**Prerequisites:** Phase 2 completed, React/Next.js knowledge, Tailwind CSS familiarity

---

## 📋 Overview

This phase creates the complete frontend application for Mizan including:
- Next.js 14 with App Router and TypeScript
- Better Auth client integration with Google OAuth
- Responsive UI with Tailwind CSS
- Hierarchy navigation (University → Department → Course)
- File listing and download functionality
- Search interface with real-time results

**Technologies:**
- Next.js 14 (React framework)
- TypeScript (Type safety)
- Tailwind CSS (Styling)
- Better Auth (Authentication)
- React Query (Data fetching)
- Headless UI (Components)

---

## 🚀 Day 1: Next.js Project Setup

### Step 1: Create Next.js Project

```bash
# 1. On your local development machine (not EC2)
mkdir -p ~/mizan/frontend
cd ~/mizan/frontend

# 2. Create Next.js project with all features
npx create-next-app@latest mizan-frontend \
  --typescript \
  --tailwind \
  --eslint \
  --app \
  --src-dir \
  --import-alias "@/*"

cd mizan-frontend

# 3. Install additional dependencies
npm install @better-auth/react @better-auth/client
npm install @tanstack/react-query @tanstack/react-query-devtools
npm install axios
npm install @headlessui/react @heroicons/react
npm install react-hot-toast
npm install clsx tailwind-merge
npm install next-themes
npm install @types/node

# 4. Install development dependencies
npm install -D @types/react @types/react-dom
```

### Step 2: Project Structure Setup

```bash
# 1. Create directory structure
mkdir -p src/{components,lib,hooks,types,constants}
mkdir -p src/components/{ui,layout,auth,hierarchy,files,search}
mkdir -p src/app/{auth,universities,search}

# 2. View structure
tree src/
```

### Step 3: Environment Configuration

```bash
# 1. Create environment variables
cat > .env.local << 'EOF'
# API Configuration
NEXT_PUBLIC_API_URL=http://<EC2_PUBLIC_IP>:3000/api
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Google OAuth (same as backend)
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id

# App Configuration
NEXT_PUBLIC_APP_NAME=Mizan
NEXT_PUBLIC_APP_DESCRIPTION=Academic Document Sharing Platform
EOF

# 2. Add to .gitignore
echo ".env.local" >> .gitignore
```

---

## 🔐 Day 1-2: Authentication Setup

### Step 1: Better Auth Client Configuration

```bash
# 1. Create auth client configuration
cat > src/lib/auth-client.ts << 'EOF'
import { createAuthClient } from "@better-auth/react";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  plugins: [],
});

export const { signIn, signOut, useSession } = authClient;
EOF
```

### Step 2: Authentication Context

```bash
# 1. Create auth provider
cat > src/components/auth/auth-provider.tsx << 'EOF'
'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import { useState } from 'react';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            retry: 1,
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
EOF
```

### Step 3: Authentication Components

```bash
# 1. Create sign-in button component
cat > src/components/auth/sign-in-button.tsx << 'EOF'
'use client';

import { useState } from 'react';
import { signIn } from '@/lib/auth-client';
import toast from 'react-hot-toast';

export function SignInButton() {
  const [isLoading, setIsLoading] = useState(false);

  const handleSignIn = async () => {
    try {
      setIsLoading(true);
      await signIn.social({
        provider: 'google',
        callbackURL: '/',
      });
    } catch (error) {
      console.error('Sign in error:', error);
      toast.error('Failed to sign in. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleSignIn}
      disabled={isLoading}
      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {isLoading ? (
        <>
          <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Signing in...
        </>
      ) : (
        <>
          <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          Sign in with Google
        </>
      )}
    </button>
  );
}
EOF
```

### Step 4: Protected Route Component

```bash
# 1. Create protected route wrapper
cat > src/components/auth/protected-route.tsx << 'EOF'
'use client';

import { useSession } from '@/lib/auth-client';
import { SignInButton } from './sign-in-button';

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { data: session, isLoading } = useSession();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full space-y-8 p-8">
            <div className="text-center">
              <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                Welcome to Mizan
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                Sign in to access academic documents
              </p>
            </div>
            <div className="flex justify-center">
              <SignInButton />
            </div>
          </div>
        </div>
      )
    );
  }

  return <>{children}</>;
}
EOF
```

---

## 🎨 Day 2-3: Layout and Navigation

### Step 1: Create Layout Components

```bash
# 1. Create header component
cat > src/components/layout/header.tsx << 'EOF'
'use client';

import Link from 'next/link';
import { useSession, signOut } from '@/lib/auth-client';
import { MagnifyingGlassIcon, UserCircleIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';

export function Header() {
  const { data: session } = useSession();
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <div className="text-2xl font-bold text-blue-600">Mizan</div>
            </Link>
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-lg mx-8">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search files..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* User Menu */}
          {session && (
            <div className="relative">
              <button
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                className="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {session.user.avatarUrl ? (
                  <img
                    className="h-8 w-8 rounded-full"
                    src={session.user.avatarUrl}
                    alt={session.user.name}
                  />
                ) : (
                  <UserCircleIcon className="h-8 w-8 text-gray-400" />
                )}
              </button>

              {isUserMenuOpen && (
                <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                  <div className="py-1">
                    <div className="px-4 py-2 text-sm text-gray-700 border-b">
                      <div className="font-medium">{session.user.name}</div>
                      <div className="text-gray-500">{session.user.email}</div>
                    </div>
                    <button
                      onClick={handleSignOut}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Sign out
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
EOF
```

### Step 2: Create Footer Component

```bash
# 1. Create footer component
cat > src/components/layout/footer.tsx << 'EOF'
import Link from 'next/link';

export function Footer() {
  return (
    <footer className="bg-gray-50 border-t border-gray-200">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-2">
            <div className="text-2xl font-bold text-blue-600 mb-4">Mizan</div>
            <p className="text-gray-600 text-sm max-w-md">
              Academic document sharing platform for Ethiopian universities. 
              Find and download past exams, lecture notes, and study materials.
            </p>
          </div>
          
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              Resources
            </h3>
            <ul className="space-y-2">
              <li>
                <Link href="/universities" className="text-gray-600 hover:text-gray-900 text-sm">
                  Browse Universities
                </Link>
              </li>
              <li>
                <Link href="/search" className="text-gray-600 hover:text-gray-900 text-sm">
                  Search Files
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              Legal
            </h3>
            <ul className="space-y-2">
              <li>
                <Link href="/terms" className="text-gray-600 hover:text-gray-900 text-sm">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-gray-600 hover:text-gray-900 text-sm">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-gray-400 text-sm text-center">
            © 2025 Mizan. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
EOF
```

### Step 3: Create Main Layout

```bash
# 1. Create main layout component
cat > src/components/layout/main-layout.tsx << 'EOF'
import { Header } from './header';
import { Footer } from './footer';

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {children}
      </main>
      <Footer />
    </div>
  );
}
EOF
```

---

## 🗂️ Day 3-4: Hierarchy Navigation

### Step 1: API Client Setup

```bash
# 1. Create API client
cat > src/lib/api-client.ts << 'EOF'
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
});

// Request interceptor to add auth token
apiClient.interceptors.request.use((config) => {
  // Add auth token if available
  const token = localStorage.getItem('auth-token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized - redirect to login
      localStorage.removeItem('auth-token');
      window.location.href = '/auth/signin';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
EOF
```

### Step 2: Type Definitions

```bash
# 1. Create type definitions
cat > src/types/index.ts << 'EOF'
export interface University {
  id: string;
  name: string;
  slug: string;
  departmentCount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Department {
  id: string;
  name: string;
  slug: string;
  universityId: string;
  courseCount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Course {
  id: string;
  name: string;
  courseCode: string;
  slug: string;
  departmentId: string;
  fileCount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface File {
  id: string;
  title: string;
  fileName: string;
  originalName: string;
  fileType: string;
  extension: string;
  fileSize: number;
  category: FileCategory;
  academicYear: number;
  semester: number;
  description?: string;
  downloadCount: number;
  viewCount: number;
  createdAt: string;
  courses: {
    id: string;
    name: string;
    courseCode: string;
    isPrimary: boolean;
  }[];
  tags: {
    id: string;
    name: string;
  }[];
}

export enum FileCategory {
  EXAM = 'EXAM',
  LECTURE_NOTE = 'LECTURE_NOTE',
  ASSIGNMENT = 'ASSIGNMENT',
  LAB_MANUAL = 'LAB_MANUAL',
  PROJECT = 'PROJECT',
  TEXTBOOK = 'TEXTBOOK',
  REFERENCE = 'REFERENCE',
  OTHER = 'OTHER',
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatarUrl?: string;
  role: 'USER' | 'ADMIN';
}

export interface PaginationInfo {
  page: number;
  limit: number;
  totalItems: number;
  totalPages: number;
}

export interface ApiResponse<T> {
  data: T;
  pagination?: PaginationInfo;
}
EOF
```

### Step 3: API Hooks

```bash
# 1. Create API hooks
cat > src/hooks/use-api.ts << 'EOF'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import apiClient from '@/lib/api-client';
import { University, Department, Course, File } from '@/types';

// Universities
export function useUniversities() {
  return useQuery({
    queryKey: ['universities'],
    queryFn: async () => {
      const response = await apiClient.get<University[]>('/universities');
      return response.data;
    },
  });
}

// Departments
export function useDepartments(universitySlug: string) {
  return useQuery({
    queryKey: ['departments', universitySlug],
    queryFn: async () => {
      const response = await apiClient.get<Department[]>(`/universities/${universitySlug}/departments`);
      return response.data;
    },
    enabled: !!universitySlug,
  });
}

// Courses
export function useCourses(universitySlug: string, departmentSlug: string) {
  return useQuery({
    queryKey: ['courses', universitySlug, departmentSlug],
    queryFn: async () => {
      const response = await apiClient.get<Course[]>(`/departments/${departmentSlug}/courses`);
      return response.data;
    },
    enabled: !!universitySlug && !!departmentSlug,
  });
}

// Files
export function useFiles(courseSlug: string, params?: {
  page?: number;
  limit?: number;
  category?: string;
  year?: number;
  semester?: number;
}) {
  return useQuery({
    queryKey: ['files', courseSlug, params],
    queryFn: async () => {
      const response = await apiClient.get(`/courses/${courseSlug}/files`, { params });
      return response.data;
    },
    enabled: !!courseSlug,
  });
}

// File download
export function useDownloadFile() {
  return useMutation({
    mutationFn: async (fileId: string) => {
      const response = await apiClient.post(`/files/${fileId}/download`);
      return response.data;
    },
  });
}

// Search
export function useSearch(query: string, params?: {
  page?: number;
  limit?: number;
  category?: string[];
  courseId?: string[];
}) {
  return useQuery({
    queryKey: ['search', query, params],
    queryFn: async () => {
      const response = await apiClient.get('/search', {
        params: { q: query, ...params },
      });
      return response.data;
    },
    enabled: !!query && query.length >= 2,
  });
}
EOF
```

---

## 📁 Day 4-5: File Listing and Downloads

### Step 1: University List Page

```bash
# 1. Create universities page
cat > src/app/universities/page.tsx << 'EOF'
'use client';

import { useUniversities } from '@/hooks/use-api';
import { MainLayout } from '@/components/layout/main-layout';
import { ProtectedRoute } from '@/components/auth/protected-route';
import Link from 'next/link';
import { BuildingOfficeIcon } from '@heroicons/react/24/outline';

export default function UniversitiesPage() {
  const { data: universities, isLoading, error } = useUniversities();

  if (isLoading) {
    return (
      <ProtectedRoute>
        <MainLayout>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="animate-pulse space-y-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </MainLayout>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <MainLayout>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center">
              <p className="text-red-600">Failed to load universities. Please try again.</p>
            </div>
          </div>
        </MainLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Universities</h1>
            <p className="mt-2 text-gray-600">
              Select a university to browse departments and courses
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {universities?.map((university) => (
              <Link
                key={university.id}
                href={`/universities/${university.slug}`}
                className="block p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-500 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center">
                  <BuildingOfficeIcon className="h-8 w-8 text-blue-600 mr-3" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {university.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {university.departmentCount || 0} departments
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {universities?.length === 0 && (
            <div className="text-center py-12">
              <BuildingOfficeIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No universities</h3>
              <p className="mt-1 text-sm text-gray-500">
                Universities will appear here once they are added by administrators.
              </p>
            </div>
          )}
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
EOF
```

### Step 2: File Download Component

```bash
# 1. Create file download component
cat > src/components/files/file-download-button.tsx << 'EOF'
'use client';

import { useState } from 'react';
import { useDownloadFile } from '@/hooks/use-api';
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface FileDownloadButtonProps {
  fileId: string;
  fileName: string;
  fileSize: number;
}

export function FileDownloadButton({ fileId, fileName, fileSize }: FileDownloadButtonProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const downloadMutation = useDownloadFile();

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDownload = async () => {
    try {
      setIsDownloading(true);
      const response = await downloadMutation.mutateAsync(fileId);
      
      // Create download link
      const link = document.createElement('a');
      link.href = response.downloadUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success('Download started');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download file');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <button
      onClick={handleDownload}
      disabled={isDownloading}
      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {isDownloading ? (
        <>
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Downloading...
        </>
      ) : (
        <>
          <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
          Download ({formatFileSize(fileSize)})
        </>
      )}
    </button>
  );
}
EOF
```

---

## 🔍 Day 5-6: Search Implementation

### Step 1: Search Page

```bash
# 1. Create search page
cat > src/app/search/page.tsx << 'EOF'
'use client';

import { useState } from 'react';
import { useSearch } from '@/hooks/use-api';
import { MainLayout } from '@/components/layout/main-layout';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { FileDownloadButton } from '@/components/files/file-download-button';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

export default function SearchPage() {
  const [query, setQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  
  const { data: searchResults, isLoading, error } = useSearch(debouncedQuery);

  // Debounce search query
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Search Files</h1>
            
            {/* Search Input */}
            <div className="relative max-w-2xl">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search for files, courses, or topics..."
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg"
              />
            </div>
          </div>

          {/* Search Results */}
          {isLoading && debouncedQuery && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Searching...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-red-600">Search failed. Please try again.</p>
            </div>
          )}

          {searchResults && (
            <div>
              <div className="mb-4">
                <p className="text-gray-600">
                  Found {searchResults.pagination?.totalItems || 0} results
                  {debouncedQuery && ` for "${debouncedQuery}"`}
                </p>
              </div>

              <div className="space-y-4">
                {searchResults.results?.map((file) => (
                  <div key={file.id} className="bg-white p-6 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {file.title}
                        </h3>
                        
                        {file.description && (
                          <p className="text-gray-600 mb-3">{file.description}</p>
                        )}
                        
                        <div className="flex flex-wrap gap-2 mb-3">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {file.category.replace('_', ' ')}
                          </span>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {file.academicYear} - Semester {file.semester}
                          </span>
                          {file.courses.map((course) => (
                            <span key={course.id} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              {course.courseCode}
                            </span>
                          ))}
                        </div>
                        
                        <div className="text-sm text-gray-500">
                          {file.downloadCount} downloads • {file.extension.toUpperCase()}
                        </div>
                      </div>
                      
                      <div className="ml-4">
                        <FileDownloadButton
                          fileId={file.id}
                          fileName={file.fileName}
                          fileSize={file.fileSize}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {searchResults.results?.length === 0 && debouncedQuery && (
                <div className="text-center py-12">
                  <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No results found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Try adjusting your search terms or browse by university.
                  </p>
                </div>
              )}
            </div>
          )}

          {!debouncedQuery && (
            <div className="text-center py-12">
              <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Start searching</h3>
              <p className="mt-1 text-sm text-gray-500">
                Enter a search term to find files, courses, or topics.
              </p>
            </div>
          )}
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
EOF
```

---

## 🚀 Day 6-7: Vercel Deployment

### Step 1: Prepare for Deployment

```bash
# 1. Update environment variables for production
cat > .env.production << 'EOF'
NEXT_PUBLIC_API_URL=http://<EC2_PUBLIC_IP>:3000/api
NEXT_PUBLIC_APP_URL=https://your-app.vercel.app
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
NEXT_PUBLIC_APP_NAME=Mizan
NEXT_PUBLIC_APP_DESCRIPTION=Academic Document Sharing Platform
EOF

# 2. Update package.json scripts
npm pkg set scripts.build="next build"
npm pkg set scripts.start="next start"
npm pkg set scripts.lint="next lint"
```

### Step 2: Deploy to Vercel

```bash
# 1. Install Vercel CLI
npm install -g vercel

# 2. Login to Vercel
vercel login

# 3. Initialize Vercel project
vercel init

# 4. Configure environment variables in Vercel dashboard
# Go to your project settings and add:
# - NEXT_PUBLIC_API_URL
# - NEXT_PUBLIC_GOOGLE_CLIENT_ID
# - Other environment variables

# 5. Deploy to production
vercel --prod

# 6. Get deployment URL
vercel ls
```

### Step 3: Update Backend CORS

```bash
# 1. SSH into EC2 and update backend CORS settings
ssh -i mizan-key.pem ubuntu@<EC2_PUBLIC_IP>
cd ~/mizan/backend/mizan-backend

# 2. Update main.ts CORS configuration
# Replace 'https://mizan.vercel.app' with your actual Vercel URL

# 3. Rebuild and restart backend
docker-compose down
docker-compose build
docker-compose up -d
```

---

## ✅ Phase 3 Completion Checklist

### Frontend Core ✅
- [ ] Next.js 14 application created with TypeScript
- [ ] Tailwind CSS configured and working
- [ ] App Router structure implemented
- [ ] Environment variables configured

### Authentication ✅
- [ ] Better Auth client integration complete
- [ ] Google OAuth flow working
- [ ] Protected routes implemented
- [ ] User session management working

### UI Components ✅
- [ ] Header with navigation and user menu
- [ ] Footer with legal links
- [ ] Main layout component
- [ ] Loading and error states

### Navigation ✅
- [ ] Universities listing page
- [ ] Departments and courses navigation
- [ ] Breadcrumb navigation
- [ ] Responsive design for mobile

### File Management ✅
- [ ] File listing with metadata
- [ ] Download functionality working
- [ ] File categorization and filtering
- [ ] File size formatting

### Search ✅
- [ ] Search interface with real-time results
- [ ] Search result display with metadata
- [ ] Debounced search queries
- [ ] Empty state handling

### Deployment ✅
- [ ] Vercel deployment configured
- [ ] Environment variables set
- [ ] Production build working
- [ ] CORS configured for frontend domain

---

## 🧪 Testing Your Frontend

```bash
# 1. Test local development
npm run dev
# Visit http://localhost:3000

# 2. Test production build
npm run build
npm run start

# 3. Test authentication flow
# - Click "Sign in with Google"
# - Verify redirect and session creation

# 4. Test navigation
# - Browse universities, departments, courses
# - Verify data loading and error states

# 5. Test search functionality
# - Search for files
# - Verify results display and download
```

---

## 🚀 Next Steps

Your frontend is now complete! Proceed to **[Phase 4: Search & Admin Integration](./PHASE_4_SEARCH_ADMIN.md)** to add Meilisearch and admin functionality.

**What you have:**
- ✅ Complete Next.js frontend with TypeScript
- ✅ Google OAuth authentication working
- ✅ Responsive UI with Tailwind CSS
- ✅ Hierarchy navigation system
- ✅ File download functionality
- ✅ Search interface ready for backend integration
- ✅ Deployed to Vercel with production configuration

**Frontend URL:** Your Vercel deployment URL
