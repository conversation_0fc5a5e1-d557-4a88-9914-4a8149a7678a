# 🔌 4. API & Data Contracts: Mizan

*   **Version:** 1.1
*   **Date:** September 19, 2025
*   **Status:** Revised
*   **Author:** <PERSON><PERSON><PERSON><PERSON> (Project Lead)
*   **Related Document:** `3_Architecture_and_Design_Overview.md`

---

## 1. General Conventions

### 1.1 Versioning

All API endpoints will be prefixed with a version number. The initial version will be `v1`.

**Base URL:** `https://api.mizan.io/v1` (domain TBD, will use EC2 public IP initially)

### 1.2 Authentication

*   Authenticated endpoints require a JSON Web Token (JWT) to be passed in the `Authorization` header as a Bearer token.
    *   `Authorization: Bearer <JWT_TOKEN>`
*   Requests to authenticated endpoints without a valid JWT will receive a `401 Unauthorized` response.

### 1.3 Data Format

*   All request and response bodies will use `application/json`.
*   All timestamps will be in **ISO 8601 format** with UTC timezone (e.g., `2025-09-08T15:30:00Z`).
*   All IDs are UUIDs represented as strings.

### 1.4 Error Handling

API errors will return a standardized JSON object and a relevant HTTP status code.

**Standard Error Response Body:**
```json
{
  "statusCode": number,    // e.g., 404
  "error": "string",       // e.g., "Not Found"
  "message": "string",     // A human-readable description
  "timestamp": "string",   // ISO 8601 timestamp
  "path": "string"         // The requested endpoint
}
```

**Common Status Codes:**
*   `400 Bad Request`: Invalid request body or parameters
*   `401 Unauthorized`: Missing or invalid authentication
*   `403 Forbidden`: Insufficient permissions
*   `404 Not Found`: Resource does not exist
*   `429 Too Many Requests`: Rate limit exceeded
*   `500 Internal Server Error`: Server-side error

---

## 2. Data Models & Schemas (Prisma Schema)

These models define the core data structures with proper many-to-many relationships.

```typescript
// --- Core Content Hierarchy ---
model University {
  id          String       @id @default(uuid())
  name        String       @unique
  slug        String       @unique
  departments Department[]
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

model Department {
  id           String     @id @default(uuid())
  name         String
  slug         String
  universityId String
  university   University @relation(fields: [universityId], references: [id], onDelete: Cascade)
  courses      Course[]
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  
  @@unique([slug, universityId])
}

model Course {
  id           String      @id @default(uuid())
  name         String      // e.g., "Software Engineering"
  courseCode   String      // e.g., "COSC-305"
  slug         String
  departmentId String
  department   Department  @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  files        FileCourse[] // Many-to-many through join table
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  
  @@unique([courseCode, departmentId])
}

// --- File & Content ---
model File {
  id            String       @id @default(uuid())
  fileName      String
  originalName  String       // Original uploaded filename
  fileType      String       // MIME type: "application/pdf", "image/jpeg"
  extension     String       // File extension: "pdf", "jpg"
  fileSize      Int         // Size in bytes
  storageKey    String       @unique // The key/path in Cloudflare R2
  fileHash      String       @unique // SHA-256 hash for duplicate detection
  
  // Metadata
  title         String       // Display title
  description   String?      // Optional description for search
  academicYear  Int         // e.g., 2024
  semester      Int         // 1 or 2
  category      FileCategory // Enum: EXAM, LECTURE_NOTE, ASSIGNMENT, etc.
  
  // Relations
  courses       FileCourse[] // Many-to-many through join table
  tags          FileTag[]    // Many-to-many through join table
  uploaderId    String?      // Admin who uploaded (null for system seeds)
  uploader      User?        @relation(fields: [uploaderId], references: [id], onDelete: SetNull)
  
  // Stats
  downloadCount Int          @default(0)
  viewCount     Int          @default(0)
  
  // Timestamps
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  deletedAt     DateTime?    // Soft delete
  
  @@index([fileHash])
  @@index([academicYear, semester])
  @@index([category])
}

// Join table for many-to-many File <-> Course
model FileCourse {
  file      File     @relation(fields: [fileId], references: [id], onDelete: Cascade)
  fileId    String
  course    Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId  String
  isPrimary Boolean  @default(false) // Is this the primary course for the file?
  
  @@id([fileId, courseId])
}

model Tag {
  id    String    @id @default(uuid())
  name  String    @unique
  slug  String    @unique
  files FileTag[]
  
  createdAt DateTime @default(now())
}

// Join table for many-to-many File <-> Tag
model FileTag {
  file   File   @relation(fields: [fileId], references: [id], onDelete: Cascade)
  fileId String
  tag    Tag    @relation(fields: [tagId], references: [id], onDelete: Cascade)
  tagId  String
  
  @@id([fileId, tagId])
}

// --- User & Auth ---
model User {
  id            String    @id @default(uuid())
  email         String    @unique
  name          String
  googleId      String    @unique // Google OAuth ID
  avatarUrl     String?
  role          UserRole  @default(USER)
  
  // Activity tracking
  lastLoginAt   DateTime?
  loginCount    Int       @default(0)
  
  // Relations
  uploadedFiles File[]
  downloads     Download[]
  searchQueries SearchQuery[]
  
  // Timestamps
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  deletedAt     DateTime? // Soft delete for banned users
  
  @@index([email])
  @@index([googleId])
}

// --- Analytics & Tracking ---
model Download {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  fileId    String
  createdAt DateTime @default(now())
  
  @@index([userId, fileId])
  @@index([createdAt])
}

model SearchQuery {
  id          String   @id @default(uuid())
  query       String
  resultsCount Int
  userId      String?
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  createdAt   DateTime @default(now())
  
  @@index([query])
  @@index([createdAt])
}

// --- Enums ---
enum UserRole {
  USER
  ADMIN
}

enum FileCategory {
  EXAM
  LECTURE_NOTE
  ASSIGNMENT
  LAB_MANUAL
  PROJECT
  TEXTBOOK
  REFERENCE
  OTHER
}
```

---

## 3. API Endpoints

### 3.1 Authentication (`/auth`)

**`POST /auth/google/callback`**
*   **Description:** Exchanges a Google OAuth authorization code for a session JWT.
*   **Auth:** Public
*   **Request Body:**
    ```json
    { "code": "string" }
    ```
*   **Success Response (`200 OK`):**
    ```json
    {
      "accessToken": "string",
      "refreshToken": "string",
      "expiresIn": 3600,
      "user": {
        "id": "uuid",
        "name": "string",
        "email": "string",
        "avatarUrl": "string",
        "role": "USER"
      }
    }
    ```

**`POST /auth/refresh`**
*   **Description:** Refreshes an expired access token.
*   **Auth:** Public
*   **Request Body:**
    ```json
    { "refreshToken": "string" }
    ```

### 3.2 Content Hierarchy (`/hierarchy`)

**`GET /universities`**
*   **Description:** Retrieves all universities.
*   **Auth:** Public
*   **Success Response (`200 OK`):**
    ```json
    [
      { 
        "id": "uuid", 
        "name": "string",
        "slug": "string",
        "departmentCount": number
      }
    ]
    ```

**`GET /universities/{slug}/departments`**
*   **Description:** Retrieves departments for a university.
*   **Auth:** Public
*   **Success Response (`200 OK`):**
    ```json
    [
      { 
        "id": "uuid", 
        "name": "string",
        "slug": "string",
        "courseCount": number
      }
    ]
    ```

**`GET /departments/{slug}/courses`**
*   **Description:** Retrieves courses for a department.
*   **Auth:** Public
*   **Success Response (`200 OK`):**
    ```json
    [
      { 
        "id": "uuid", 
        "name": "string",
        "courseCode": "string",
        "slug": "string",
        "fileCount": number
      }
    ]
    ```

### 3.3 Files (`/files`)

**`GET /courses/{courseSlug}/files`**
*   **Description:** Retrieves paginated files for a course.
*   **Auth:** Required
*   **Query Parameters:**
    *   `page: number` (default: 1)
    *   `limit: number` (default: 20, max: 50)
    *   `category: FileCategory` (optional filter)
    *   `year: number` (optional filter)
    *   `semester: number` (optional filter)
*   **Success Response (`200 OK`):**
    ```json
    {
      "pagination": { 
        "page": 1, 
        "limit": 20, 
        "totalItems": 150, 
        "totalPages": 8 
      },
      "files": [
        {
          "id": "uuid",
          "title": "string",
          "fileName": "string",
          "fileType": "application/pdf",
          "fileSize": number,
          "category": "EXAM",
          "academicYear": 2024,
          "semester": 1,
          "description": "string",
          "courses": [
            {
              "id": "uuid",
              "name": "string",
              "courseCode": "string",
              "isPrimary": true
            }
          ],
          "tags": [
            { "id": "uuid", "name": "string" }
          ],
          "downloadCount": number,
          "createdAt": "ISO8601"
        }
      ]
    }
    ```

**`GET /files/{fileId}`**
*   **Description:** Get detailed information about a specific file.
*   **Auth:** Required
*   **Success Response (`200 OK`):** Same structure as individual file in list above.

**`POST /files/{fileId}/download`**
*   **Description:** Generates a presigned download URL and logs the download.
*   **Auth:** Required
*   **Success Response (`200 OK`):**
    ```json
    { 
      "downloadUrl": "string",
      "expiresIn": 300,
      "fileName": "string"
    }
    ```

### 3.4 Search (`/search`)

**`GET /search`**
*   **Description:** Performs full-text search across files.
*   **Auth:** Required
*   **Query Parameters:**
    *   `q: string` (search query, min 2 chars)
    *   `page: number` (default: 1)
    *   `limit: number` (default: 20)
    *   `category: FileCategory[]` (optional filters)
    *   `courseId: string[]` (optional filters)
*   **Success Response (`200 OK`):**
    ```json
    {
      "query": "string",
      "pagination": { ... },
      "results": [ ... ], // Same structure as files list
      "facets": {
        "categories": [
          { "value": "EXAM", "count": 45 },
          { "value": "LECTURE_NOTE", "count": 32 }
        ],
        "years": [
          { "value": 2024, "count": 67 },
          { "value": 2023, "count": 43 }
        ]
      },
      "processingTimeMs": number
    }
    ```

### 3.5 Health & Monitoring

**`GET /health`**
*   **Description:** Basic health check for load balancer.
*   **Auth:** Public
*   **Success Response (`200 OK`):**
    ```json
    {
      "status": "ok",
      "timestamp": "ISO8601",
      "version": "1.0.0"
    }
    ```

**`GET /health/detailed`**
*   **Description:** Detailed health check including dependencies.
*   **Auth:** Admin only
*   **Success Response (`200 OK`):**
    ```json
    {
      "status": "ok",
      "services": {
        "database": "ok",
        "search": "ok",
        "storage": "ok"
      },
      "metrics": {
        "uptime": number,
        "memoryUsage": { ... },
        "requestsPerMinute": number
      }
    }
    ```