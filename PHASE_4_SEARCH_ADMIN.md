# 🔍 Phase 4: Search & Admin Integration (Week 4)

**Objective:** Integrate Meilisearch for powerful search capabilities and setup Retool admin panel for content management.

**Duration:** 7 days  
**Prerequisites:** Phase 3 completed, backend and frontend working

---

## 📋 Overview

This phase adds advanced search capabilities and administrative tools:
- Meilisearch integration for fast, typo-tolerant search
- Retool admin panel for content management
- File upload workflow with R2 integration
- Enhanced security and rate limiting
- Comprehensive monitoring with Sentry and PostHog
- Complete stack deployment

**Technologies:**
- <PERSON><PERSON>earch (Search engine)
- Retool (Admin panel)
- Sentry (Error tracking)
- PostHog (Analytics)
- Enhanced security middleware

---

## 🔍 Day 1-2: Meilisearch Integration

### Step 1: Add Meilisearch to Backend

```bash
# 1. SSH into EC2 instance
ssh -i mizan-key.pem ubuntu@<EC2_PUBLIC_IP>
cd ~/mizan/backend/mizan-backend

# 2. Install Meilisearch client
npm install meilisearch

# 3. Update docker-compose.yml to include <PERSON><PERSON>ear<PERSON>
cat >> docker-compose.yml << 'EOF'

  meilisearch:
    image: getmeili/meilisearch:v1.9
    ports:
      - "7700:7700"
    volumes:
      - ./meili_data:/meili_data
    environment:
      - MEILI_MASTER_KEY=${MEILISEARCH_MASTER_KEY}
      - MEILI_ENV=production
      - MEILI_HTTP_ADDR=0.0.0.0:7700
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7700/health"]
      interval: 30s
      timeout: 10s
      retries: 3
EOF

# 4. Update environment variables
echo "MEILISEARCH_MASTER_KEY=$(openssl rand -base64 32)" >> .env
```

### Step 2: Create Meilisearch Service

```bash
# 1. Create Meilisearch service
cat > src/search/meilisearch.service.ts << 'EOF'
import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MeiliSearch, Index } from 'meilisearch';
import { File, FileCategory } from '@prisma/client';

@Injectable()
export class MeilisearchService implements OnModuleInit {
  private client: MeiliSearch;
  private filesIndex: Index;

  constructor(private configService: ConfigService) {
    this.client = new MeiliSearch({
      host: this.configService.get('MEILISEARCH_URL'),
      apiKey: this.configService.get('MEILISEARCH_MASTER_KEY'),
    });
  }

  async onModuleInit() {
    try {
      // Create files index
      this.filesIndex = this.client.index('files');
      
      // Configure searchable attributes
      await this.filesIndex.updateSearchableAttributes([
        'title',
        'description',
        'fileName',
        'originalName',
        'courseNames',
        'tagNames',
        'category',
      ]);

      // Configure filterable attributes
      await this.filesIndex.updateFilterableAttributes([
        'category',
        'academicYear',
        'semester',
        'courseIds',
        'tagIds',
      ]);

      // Configure sortable attributes
      await this.filesIndex.updateSortableAttributes([
        'createdAt',
        'downloadCount',
        'viewCount',
        'academicYear',
      ]);

      console.log('✅ Meilisearch initialized successfully');
    } catch (error) {
      console.error('❌ Meilisearch initialization failed:', error);
    }
  }

  async indexFile(file: File & {
    courses: Array<{ id: string; name: string; courseCode: string }>;
    tags: Array<{ id: string; name: string }>;
  }) {
    const document = {
      id: file.id,
      title: file.title,
      description: file.description,
      fileName: file.fileName,
      originalName: file.originalName,
      category: file.category,
      academicYear: file.academicYear,
      semester: file.semester,
      fileSize: file.fileSize,
      downloadCount: file.downloadCount,
      viewCount: file.viewCount,
      createdAt: file.createdAt.getTime(),
      
      // Searchable course and tag names
      courseNames: file.courses.map(c => `${c.name} ${c.courseCode}`).join(' '),
      tagNames: file.tags.map(t => t.name).join(' '),
      
      // Filterable IDs
      courseIds: file.courses.map(c => c.id),
      tagIds: file.tags.map(t => t.id),
    };

    await this.filesIndex.addDocuments([document]);
  }

  async indexFiles(files: Array<File & {
    courses: Array<{ id: string; name: string; courseCode: string }>;
    tags: Array<{ id: string; name: string }>;
  }>) {
    const documents = files.map(file => ({
      id: file.id,
      title: file.title,
      description: file.description,
      fileName: file.fileName,
      originalName: file.originalName,
      category: file.category,
      academicYear: file.academicYear,
      semester: file.semester,
      fileSize: file.fileSize,
      downloadCount: file.downloadCount,
      viewCount: file.viewCount,
      createdAt: file.createdAt.getTime(),
      courseNames: file.courses.map(c => `${c.name} ${c.courseCode}`).join(' '),
      tagNames: file.tags.map(t => t.name).join(' '),
      courseIds: file.courses.map(c => c.id),
      tagIds: file.tags.map(t => t.id),
    }));

    await this.filesIndex.addDocuments(documents);
  }

  async searchFiles(query: string, options: {
    page?: number;
    limit?: number;
    filters?: string[];
    sort?: string[];
  } = {}) {
    const { page = 1, limit = 20, filters = [], sort = [] } = options;
    
    const searchParams = {
      q: query,
      offset: (page - 1) * limit,
      limit,
      filter: filters.length > 0 ? filters : undefined,
      sort: sort.length > 0 ? sort : ['downloadCount:desc', 'createdAt:desc'],
      attributesToHighlight: ['title', 'description'],
      highlightPreTag: '<mark>',
      highlightPostTag: '</mark>',
    };

    const results = await this.filesIndex.search(query, searchParams);
    
    return {
      hits: results.hits,
      totalHits: results.estimatedTotalHits,
      processingTimeMs: results.processingTimeMs,
      query: results.query,
      page,
      limit,
      totalPages: Math.ceil(results.estimatedTotalHits / limit),
    };
  }

  async deleteFile(fileId: string) {
    await this.filesIndex.deleteDocument(fileId);
  }

  async reindexAll() {
    // This will be called from admin endpoint
    await this.filesIndex.deleteAllDocuments();
    // Files will be reindexed by the calling service
  }

  async getStats() {
    const stats = await this.filesIndex.getStats();
    return {
      numberOfDocuments: stats.numberOfDocuments,
      isIndexing: stats.isIndexing,
      fieldDistribution: stats.fieldDistribution,
    };
  }

  async health() {
    return await this.client.health();
  }
}
EOF
```

### Step 3: Update Search Controller

```bash
# 1. Update search controller to use Meilisearch
cat > src/search/search.controller.ts << 'EOF'
import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { MeilisearchService } from './meilisearch.service';
import { PrismaService } from '../prisma.service';
import { IsOptional, IsString, IsNumber, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';

class SearchQueryDto {
  @IsString()
  q: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 20;

  @IsOptional()
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  category?: string[];

  @IsOptional()
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  courseId?: string[];

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  year?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(2)
  semester?: number;
}

@Controller('search')
@UseGuards(JwtAuthGuard)
export class SearchController {
  constructor(
    private meilisearchService: MeilisearchService,
    private prismaService: PrismaService,
  ) {}

  @Get()
  async search(@Query() query: SearchQueryDto) {
    const { q, page, limit, category, courseId, year, semester } = query;

    // Build filters
    const filters = [];
    if (category?.length) {
      filters.push(`category IN [${category.map(c => `"${c}"`).join(', ')}]`);
    }
    if (courseId?.length) {
      filters.push(`courseIds IN [${courseId.map(id => `"${id}"`).join(', ')}]`);
    }
    if (year) {
      filters.push(`academicYear = ${year}`);
    }
    if (semester) {
      filters.push(`semester = ${semester}`);
    }

    // Search using Meilisearch
    const searchResults = await this.meilisearchService.searchFiles(q, {
      page,
      limit,
      filters,
    });

    // Get full file details from database
    const fileIds = searchResults.hits.map(hit => hit.id);
    const files = await this.prismaService.file.findMany({
      where: {
        id: { in: fileIds },
        deletedAt: null,
      },
      include: {
        courses: {
          include: {
            course: {
              select: {
                id: true,
                name: true,
                courseCode: true,
              },
            },
          },
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // Maintain search result order
    const orderedFiles = fileIds.map(id => 
      files.find(file => file.id === id)
    ).filter(Boolean);

    // Log search query for analytics
    await this.prismaService.searchQuery.create({
      data: {
        query: q,
        resultsCount: searchResults.totalHits,
        // userId will be added when user context is available
      },
    });

    return {
      query: q,
      results: orderedFiles.map(file => ({
        id: file.id,
        title: file.title,
        fileName: file.fileName,
        fileType: file.fileType,
        extension: file.extension,
        fileSize: file.fileSize,
        category: file.category,
        academicYear: file.academicYear,
        semester: file.semester,
        description: file.description,
        downloadCount: file.downloadCount,
        viewCount: file.viewCount,
        createdAt: file.createdAt,
        courses: file.courses.map(fc => ({
          id: fc.course.id,
          name: fc.course.name,
          courseCode: fc.course.courseCode,
          isPrimary: fc.isPrimary,
        })),
        tags: file.tags.map(ft => ({
          id: ft.tag.id,
          name: ft.tag.name,
        })),
      })),
      pagination: {
        page: searchResults.page,
        limit: searchResults.limit,
        totalItems: searchResults.totalHits,
        totalPages: searchResults.totalPages,
      },
      processingTimeMs: searchResults.processingTimeMs,
    };
  }
}
EOF
```

### Step 4: Start Meilisearch Service

```bash
# 1. Update backend service dependencies in docker-compose.yml
# Add meilisearch to backend depends_on

# 2. Start services
docker-compose up -d

# 3. Check Meilisearch health
curl http://localhost:7700/health

# 4. Test search endpoint (will be empty until files are indexed)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3000/api/search?q=test"
```

---

## 🛠️ Day 2-3: Retool Admin Panel Setup

### Step 1: Create Retool Account

```bash
# 1. Sign up for Retool
# Visit: https://retool.com/
# Use your work/project email
# Select "Free" plan (supports up to 5 users)

# 2. Create new app
# Click "Create new" > "App"
# Name: "Mizan Admin Panel"
```

### Step 2: Connect Database

```bash
# 1. Add PostgreSQL resource
# In Retool: Resources > Create new > PostgreSQL
# Configuration:
# - Name: "Mizan Database"
# - Host: <RDS_ENDPOINT>
# - Port: 5432
# - Database name: mizan
# - Username: mizanadmin
# - Password: SecurePassword123!
# - SSL Mode: require

# 2. Test connection
# Click "Test connection" - should succeed
```

### Step 3: Create Admin Interfaces

```bash
# 1. Universities Management Interface
# - Create table component connected to University model
# - Add CRUD operations (Create, Read, Update, Delete)
# - Include slug auto-generation from name

# 2. Departments Management Interface
# - Create table with University relationship
# - Add dropdown for university selection
# - Include course count display

# 3. Courses Management Interface
# - Create table with Department relationship
# - Add course code validation
# - Include file count display

# 4. Files Management Interface
# - Create table with file metadata
# - Add file upload component
# - Include course association interface
# - Add tag management

# 5. Users Management Interface
# - Create table showing all users
# - Add user role management
# - Include activity statistics
# - Add ban/suspend functionality

# 6. Analytics Dashboard
# - Total users, files, downloads
# - Top downloaded files
# - Search queries with zero results
# - Daily/weekly activity charts
```

### Step 4: File Upload Workflow

```bash
# 1. Create file upload API endpoint in backend
cat > src/files/admin-files.controller.ts << 'EOF'
import { Controller, Post, Body, UseGuards, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AdminGuard } from '../auth/admin.guard';
import { FilesService } from './files.service';
import { R2Service } from './r2.service';
import { MeilisearchService } from '../search/meilisearch.service';
import * as crypto from 'crypto';

@Controller('admin/files')
@UseGuards(JwtAuthGuard, AdminGuard)
export class AdminFilesController {
  constructor(
    private filesService: FilesService,
    private r2Service: R2Service,
    private meilisearchService: MeilisearchService,
  ) {}

  @Post('upload-url')
  async getUploadUrl(@Body() body: { fileName: string; fileType: string }) {
    const { fileName, fileType } = body;
    const fileKey = `files/${Date.now()}-${fileName}`;
    
    const uploadUrl = await this.r2Service.getPresignedUploadUrl(fileKey, fileType);
    
    return {
      uploadUrl,
      fileKey,
      expiresIn: 300, // 5 minutes
    };
  }

  @Post()
  async createFile(@Body() createFileDto: {
    title: string;
    description?: string;
    fileName: string;
    originalName: string;
    fileType: string;
    fileSize: number;
    storageKey: string;
    academicYear: number;
    semester: number;
    category: string;
    courseIds: string[];
    tagNames?: string[];
  }) {
    // Calculate file hash (would be done during upload in production)
    const fileHash = crypto.createHash('sha256')
      .update(`${createFileDto.storageKey}-${createFileDto.fileSize}`)
      .digest('hex');

    // Create file record
    const file = await this.filesService.create({
      ...createFileDto,
      fileHash,
      extension: createFileDto.fileName.split('.').pop()?.toLowerCase() || '',
    });

    // Index in Meilisearch
    const fileWithRelations = await this.filesService.findById(file.id);
    await this.meilisearchService.indexFile(fileWithRelations);

    return file;
  }

  @Post('reindex')
  async reindexAll() {
    await this.meilisearchService.reindexAll();
    
    // Get all files and reindex
    const files = await this.filesService.findAllWithRelations();
    await this.meilisearchService.indexFiles(files);
    
    return { message: 'Reindexing completed', count: files.length };
  }
}
EOF
```

---

## 🔒 Day 3-4: Enhanced Security and Rate Limiting

### Step 1: Advanced Rate Limiting

```bash
# 1. Install Redis for distributed rate limiting (optional)
npm install ioredis @nestjs/throttler

# 2. Create advanced rate limiting configuration
cat > src/common/rate-limiting.config.ts << 'EOF'
import { ThrottlerModule } from '@nestjs/throttler';
import { ConfigModule, ConfigService } from '@nestjs/config';

export const RateLimitingConfig = ThrottlerModule.forRootAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: (configService: ConfigService) => ({
    ttl: 60, // 1 minute
    limit: 60, // 60 requests per minute
    storage: configService.get('NODE_ENV') === 'production' 
      ? undefined // Use Redis in production
      : undefined, // Use memory in development
  }),
});
EOF

# 3. Create custom rate limiting decorators
cat > src/common/decorators/rate-limit.decorator.ts << 'EOF'
import { SetMetadata } from '@nestjs/common';

export const RATE_LIMIT_KEY = 'rate_limit';

export interface RateLimitOptions {
  ttl: number; // Time to live in seconds
  limit: number; // Number of requests
}

export const RateLimit = (options: RateLimitOptions) => 
  SetMetadata(RATE_LIMIT_KEY, options);

// Predefined rate limits
export const DownloadRateLimit = () => RateLimit({ ttl: 60, limit: 5 }); // 5 downloads per minute
export const SearchRateLimit = () => RateLimit({ ttl: 3600, limit: 100 }); // 100 searches per hour
export const AuthRateLimit = () => RateLimit({ ttl: 900, limit: 5 }); // 5 auth attempts per 15 minutes
EOF
```

### Step 2: Input Validation and Sanitization

```bash
# 1. Create validation pipes
cat > src/common/pipes/validation.pipe.ts << 'EOF'
import { ValidationPipe as NestValidationPipe, BadRequestException } from '@nestjs/common';
import { ValidationError } from 'class-validator';

export class CustomValidationPipe extends NestValidationPipe {
  constructor() {
    super({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      disableErrorMessages: process.env.NODE_ENV === 'production',
      exceptionFactory: (errors: ValidationError[]) => {
        const messages = errors.map(error => {
          return Object.values(error.constraints || {}).join(', ');
        });
        return new BadRequestException({
          statusCode: 400,
          error: 'Validation failed',
          message: messages,
        });
      },
    });
  }
}
EOF

# 2. Create file upload validation
cat > src/common/validators/file-upload.validator.ts << 'EOF'
import { BadRequestException } from '@nestjs/common';

export class FileUploadValidator {
  private static readonly ALLOWED_MIME_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'image/jpeg',
    'image/png',
    'image/gif',
  ];

  private static readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

  static validate(file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    if (!this.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
      throw new BadRequestException('File type not allowed');
    }

    if (file.size > this.MAX_FILE_SIZE) {
      throw new BadRequestException('File size too large (max 50MB)');
    }

    return true;
  }
}
EOF
```

### Step 3: Security Headers and CORS

```bash
# 1. Update main.ts with comprehensive security
cat > src/main.ts << 'EOF'
import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import * as compression from 'compression';
import rateLimit from 'express-rate-limit';
import { AppModule } from './app.module';
import { CustomValidationPipe } from './common/pipes/validation.pipe';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Security headers
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
  }));

  // Compression
  app.use(compression());

  // Global rate limiting
  app.use(
    rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // limit each IP to 1000 requests per windowMs
      message: {
        statusCode: 429,
        error: 'Too Many Requests',
        message: 'Rate limit exceeded',
      },
      standardHeaders: true,
      legacyHeaders: false,
    }),
  );

  // CORS
  app.enableCors({
    origin: configService.get('NODE_ENV') === 'production' 
      ? [
          'https://your-frontend-domain.vercel.app',
          'https://retool.com',
          'https://*.retool.com',
        ]
      : true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  });

  // Global validation pipe
  app.useGlobalPipes(new CustomValidationPipe());

  // Global prefix
  app.setGlobalPrefix('api');

  const port = configService.get('port', 3000);
  await app.listen(port);
  
  console.log(`🚀 Application running on: http://localhost:${port}/api`);
  console.log(`🔍 Meilisearch running on: http://localhost:7700`);
}

bootstrap();
EOF
```

---

## 📊 Day 4-5: Monitoring Services Setup

### Step 1: Sentry Integration

```bash
# 1. Install Sentry
npm install @sentry/node @sentry/nestjs

# 2. Create Sentry account and project
# Visit: https://sentry.io/
# Create new project for Node.js
# Copy DSN

# 3. Configure Sentry
cat > src/common/sentry.config.ts << 'EOF'
import * as Sentry from '@sentry/node';
import { ConfigService } from '@nestjs/config';

export function configureSentry(configService: ConfigService) {
  Sentry.init({
    dsn: configService.get('SENTRY_DSN'),
    environment: configService.get('NODE_ENV'),
    tracesSampleRate: configService.get('NODE_ENV') === 'production' ? 0.1 : 1.0,
    beforeSend(event) {
      // Filter out sensitive data
      if (event.request?.headers) {
        delete event.request.headers.authorization;
        delete event.request.headers.cookie;
      }
      return event;
    },
  });
}
EOF

# 4. Add to environment variables
echo "SENTRY_DSN=your-sentry-dsn-here" >> .env
```

### Step 2: PostHog Analytics

```bash
# 1. Install PostHog
npm install posthog-node

# 2. Create PostHog account
# Visit: https://posthog.com/
# Create new project
# Copy API key

# 3. Create analytics service
cat > src/common/analytics.service.ts << 'EOF'
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PostHog } from 'posthog-node';

@Injectable()
export class AnalyticsService {
  private posthog: PostHog;

  constructor(private configService: ConfigService) {
    this.posthog = new PostHog(
      configService.get('POSTHOG_API_KEY'),
      {
        host: 'https://app.posthog.com',
      }
    );
  }

  trackEvent(userId: string, event: string, properties?: Record<string, any>) {
    if (this.configService.get('NODE_ENV') === 'production') {
      this.posthog.capture({
        distinctId: userId,
        event,
        properties,
      });
    }
  }

  trackFileDownload(userId: string, fileId: string, fileName: string) {
    this.trackEvent(userId, 'file_downloaded', {
      fileId,
      fileName,
      timestamp: new Date().toISOString(),
    });
  }

  trackSearch(userId: string, query: string, resultsCount: number) {
    this.trackEvent(userId, 'search_performed', {
      query,
      resultsCount,
      timestamp: new Date().toISOString(),
    });
  }

  trackUserSignup(userId: string, email: string) {
    this.trackEvent(userId, 'user_signed_up', {
      email,
      timestamp: new Date().toISOString(),
    });
  }

  async shutdown() {
    await this.posthog.shutdown();
  }
}
EOF

# 4. Add to environment variables
echo "POSTHOG_API_KEY=your-posthog-api-key-here" >> .env
```

### Step 3: Application Logging

```bash
# 1. Install Winston for structured logging
npm install winston winston-daily-rotate-file

# 2. Create logging configuration
cat > src/common/logger.config.ts << 'EOF'
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';
import 'winston-daily-rotate-file';

const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
);

export const LoggerConfig = WinstonModule.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports: [
    // Console transport
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple(),
      ),
    }),
    
    // File transport for errors
    new winston.transports.DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxSize: '20m',
      maxFiles: '14d',
    }),
    
    // File transport for all logs
    new winston.transports.DailyRotateFile({
      filename: 'logs/combined-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d',
    }),
  ],
});
EOF
```

---

## 🚀 Day 5-6: Complete Stack Deployment

### Step 1: Update Docker Configuration

```bash
# 1. Create production docker-compose.yml
cat > docker-compose.prod.yml << 'EOF'
version: '3.8'

services:
  backend:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_CALLBACK_URL=${GOOGLE_CALLBACK_URL}
      - R2_ACCOUNT_ID=${R2_ACCOUNT_ID}
      - R2_ACCESS_KEY_ID=${R2_ACCESS_KEY_ID}
      - R2_SECRET_ACCESS_KEY=${R2_SECRET_ACCESS_KEY}
      - R2_BUCKET_NAME=${R2_BUCKET_NAME}
      - R2_ENDPOINT=${R2_ENDPOINT}
      - MEILISEARCH_URL=http://meilisearch:7700
      - MEILISEARCH_MASTER_KEY=${MEILISEARCH_MASTER_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
      - POSTHOG_API_KEY=${POSTHOG_API_KEY}
    depends_on:
      - meilisearch
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - ./logs:/app/logs

  meilisearch:
    image: getmeili/meilisearch:v1.9
    ports:
      - "7700:7700"
    volumes:
      - ./meili_data:/meili_data
    environment:
      - MEILI_MASTER_KEY=${MEILISEARCH_MASTER_KEY}
      - MEILI_ENV=production
      - MEILI_HTTP_ADDR=0.0.0.0:7700
      - MEILI_DB_PATH=/meili_data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7700/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    restart: unless-stopped
EOF
```

### Step 2: Create Nginx Configuration

```bash
# 1. Create Nginx configuration
cat > nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:3000;
    }

    upstream meilisearch {
        server meilisearch:7700;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=search:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=download:10m rate=2r/s;

    server {
        listen 80;
        server_name _;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Search endpoints with stricter rate limiting
        location /api/search {
            limit_req zone=search burst=10 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Download endpoints with strictest rate limiting
        location /api/files/*/download {
            limit_req zone=download burst=5 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check
        location /health {
            proxy_pass http://backend/api/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Admin access to Meilisearch (restrict in production)
        location /meilisearch/ {
            # Add IP whitelist here
            # allow ***********/24;
            # deny all;
            
            proxy_pass http://meilisearch/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
EOF
```

### Step 3: Deploy Complete Stack

```bash
# 1. Create deployment script
cat > deploy.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 Starting Mizan deployment..."

# Pull latest code
git pull origin main

# Build and deploy services
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 30

# Check service health
docker-compose -f docker-compose.prod.yml ps

# Run database migrations if needed
docker-compose -f docker-compose.prod.yml exec backend npx prisma db push

# Test endpoints
echo "🧪 Testing endpoints..."
curl -f http://localhost/health || echo "❌ Health check failed"
curl -f http://localhost:7700/health || echo "❌ Meilisearch health check failed"

echo "✅ Deployment completed successfully!"
echo "🌐 API available at: http://<EC2_PUBLIC_IP>"
echo "🔍 Meilisearch available at: http://<EC2_PUBLIC_IP>:7700"
EOF

chmod +x deploy.sh

# 2. Run deployment
./deploy.sh
```

---

## ✅ Phase 4 Completion Checklist

### Search Integration ✅
- [ ] Meilisearch service running and healthy
- [ ] Search indexing pipeline implemented
- [ ] Advanced search with filters working
- [ ] Search analytics tracking implemented

### Admin Panel ✅
- [ ] Retool account created and configured
- [ ] Database connection established
- [ ] CRUD interfaces for all entities created
- [ ] File upload workflow implemented
- [ ] User management interface working
- [ ] Analytics dashboard displaying data

### Security ✅
- [ ] Advanced rate limiting implemented
- [ ] Input validation and sanitization working
- [ ] Security headers configured
- [ ] File upload validation implemented
- [ ] CORS properly configured for production

### Monitoring ✅
- [ ] Sentry error tracking configured
- [ ] PostHog analytics tracking events
- [ ] Structured logging with Winston
- [ ] Health checks for all services
- [ ] Performance monitoring active

### Deployment ✅
- [ ] Production Docker configuration ready
- [ ] Nginx reverse proxy configured
- [ ] SSL/TLS configuration prepared
- [ ] Automated deployment script working
- [ ] All services running and healthy

---

## 🧪 Testing Phase 4

```bash
# 1. Test Meilisearch
curl http://<EC2_PUBLIC_IP>:7700/health

# 2. Test search functionality
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://<EC2_PUBLIC_IP>/api/search?q=test"

# 3. Test admin endpoints
curl -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  "http://<EC2_PUBLIC_IP>/api/admin/files/reindex" \
  -X POST

# 4. Check service logs
docker-compose -f docker-compose.prod.yml logs backend
docker-compose -f docker-compose.prod.yml logs meilisearch
```

---

## 🚀 Next Steps

Your search and admin systems are now complete! Proceed to **[Phase 5: Launch Preparation](./PHASE_5_LAUNCH.md)** for content seeding, testing, and launch.

**What you have:**
- ✅ Fast, typo-tolerant search with Meilisearch
- ✅ Complete admin panel with Retool
- ✅ File upload and management system
- ✅ Enhanced security and rate limiting
- ✅ Comprehensive monitoring and analytics
- ✅ Production-ready deployment configuration

**Admin Panel:** Your Retool app URL  
**Search API:** `http://<EC2_PUBLIC_IP>/api/search`
