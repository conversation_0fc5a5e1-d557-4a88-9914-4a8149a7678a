# 🗂️ 6. Decision Log: Mizan

*   **Version:** 1.0
*   **Date:** September 8, 2025
*   **Status:** Baseline
*   **Author:** <PERSON><PERSON><PERSON><PERSON> (Project Lead)
*   **Purpose:** This document records all significant architectural, product, and strategic decisions made throughout the project's lifecycle. It serves as a permanent reference to understand the "why" behind our choices.

---

| Date       | Decision                                                            | Rationale                                                                                                                                                                                                                                                                                                     | Alternatives Considered                                       | Impact                                                                                                                                                           |
| :--------- | :------------------------------------------------------------------ | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------ | :--------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 2025-09-08 | **Adopt a "Read-Only" MVP Strategy, deferring user uploads.**       | **Primary Goal: De-risk the project.** This approach allows us to validate the core value proposition (the quality and organization of the content) with the minimum possible engineering effort. It drastically simplifies the initial scope, removing the complexities of content moderation, user contribution workflows, and advanced security concerns. | An MVP with user uploads and gamification.                     | Reduces MVP codebase by an estimated 60%. Defers all moderation and contribution features to a later phase. Focuses all initial effort on content discovery and access. |
| 2025-09-08 | **Utilize Cloudflare R2 for all object storage.**                   | **Primary Goal: Eliminate long-term costs.** Cloudflare R2's key feature is its **zero egress fee** policy, which is a massive financial advantage over competitors for a download-heavy application. It also offers a generous 10GB free tier and an S3-compatible API, preventing vendor lock-in. | AWS S3 (industry standard but with high egress costs), Backblaze B2. | Minimal code impact due to S3-compatible API. Significant positive financial impact, making the project sustainable at scale with minimal cost.                 |
| 2025-09-08 | **Use a third-party tool (Retool) for the Admin Panel.**            | **Primary Goal: Accelerate MVP development.** Building a robust, secure, and feature-rich admin panel is a significant undertaking. Retool provides a production-grade panel with minimal setup, saving weeks of development time. It allows us to focus on the core user-facing application.    | Building a custom admin panel from scratch, Forest Admin, Appsmith. | Reduces initial development scope by ~30%. Creates a dependency on Retool for administration, which is an acceptable trade-off for the speed gained.              |
| 2025-09-08 | **Integrate Meilisearch for search from Day 1.**                    | **Primary Goal: Ensure a superior user experience.** PostgreSQL's FTS lacks crucial features like typo-tolerance and advanced relevance ranking. Meilisearch provides a fast, "magical" search experience out of the box, which is a critical feature for a content-heavy platform. The setup overhead is minimal. | PostgreSQL Full-Text Search, Algolia (SaaS, more expensive), Elasticsearch (too heavyweight). | Adds one additional service to the infrastructure stack. Requires an asynchronous pipeline for indexing new content but guarantees a high-quality search feature at launch. |
| 2025-09-08 | **Select `better-auth` for the authentication library.**            | **Primary Goal: Improve developer experience and type safety.** `better-auth` offers native, first-class integration with our chosen stack (TypeScript, Prisma). This reduces boilerplate code, simplifies configuration, and minimizes the risk of type-related bugs compared to other solutions. | NextAuth.js (the original choice), building a custom solution. | Simplifies authentication logic in the backend. Creates a tight, type-safe integration between our authentication layer and database models.                   |
| 2025-09-08 | **Use PostHog for analytics instead of simpler alternatives.**      | **Primary Goal: Enable data-driven product decisions.** PostHog provides critical tools beyond basic page views, such as funnel analysis and user cohort tracking. This data is essential for understanding user behavior and iterating on the product effectively after launch.                      | Vercel Analytics (too basic), Google Analytics.                | Requires slightly more complex initial setup for event tracking but provides far more actionable insights than simpler alternatives.                                 |
| 2025-09-08 | **Design the database schema with many-to-many relationships.**     | **Primary Goal: Reflect the real-world use case.** Academic files are often relevant to multiple courses or topics. A schema with many-to-many relationships for `Files <-> Courses` and `Files <-> Tags` provides the flexibility needed to accurately model this reality from the start. | A simpler schema with one-to-many relationships.                | Increases database complexity slightly but prevents the need for a major, breaking schema migration in the future. It makes the data model more robust and scalable. |
| 2025-09-19 | **Replace DigitalOcean with AWS for hosting infrastructure.**       | **Primary Goal: Reduce hosting costs and leverage existing resources.** DigitalOcean requires a minimum $5/month payment even for GitHub Student accounts, while AWS provides a comprehensive free tier that better suits the project's budget constraints during development and early stages. | Continuing with DigitalOcean, exploring other cloud providers. | Minimal code impact due to containerized deployment strategy. Significant cost savings during development phase and access to broader AWS ecosystem services. |