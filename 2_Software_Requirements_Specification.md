# 📋 2. Software Requirements Specification (SRS): <PERSON>zan

*   **Version:** 1.1
*   **Date:** September 19, 2025
*   **Status:** Revised
*   **Author:** <PERSON><PERSON><PERSON><PERSON> (Project Lead)
*   **Related Document:** `1_Project_Vision_and_Scope.md`

---

## 1. Introduction

### 1.1 Purpose

This document provides a detailed specification of the requirements for Project Mizan. It defines the functional and non-functional requirements for the Minimum Viable Product (MVP). It will serve as the single source of truth for the development team, guiding all design, implementation, and testing efforts.

### 1.2 System Overview

Mizan is a decoupled web application comprising three main components:
1.  **A Next.js Frontend:** A server-side rendered (SSR) web client responsible for all user-facing interactions.
2.  **A Headless Backend API:** A service responsible for business logic, data access, and authentication.
3.  **Third-Party Services:** Managed services for the database, file storage, search, and admin panel.

The system's primary function is to allow authenticated users to browse, search for, and download academic documents seeded by an administrative team.

---

## 2. Functional Requirements

This section details the specific behaviors and features of the system, structured by user role.

### 2.1 User-Facing Application

| ID    | Requirement                                    | Description                                                                                                                                                              | Acceptance Criteria                                                                                                                                                                                                                                                                                                                           |
| :---- | :--------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **FR-1**  | **User Authentication**                        | Users must be able to securely sign in and out of the application.                                                                                                       | 1.1: A "Sign In" button is present on the homepage.<br>1.2: Clicking "Sign In" initiates a Google OAuth 2.0 flow.<br>1.3: Upon successful authentication with Google, the user is redirected back to the application and is in a logged-in state.<br>1.4: A logged-in user can log out, terminating their session.                 |
| **FR-2**  | **Hierarchical Content Browsing**              | Users must be able to navigate through a structured hierarchy to find courses and their associated materials.                                                            | 2.1: The homepage displays a list of participating Universities.<br>2.2: Clicking a University leads to a page listing its Departments.<br>2.3: Clicking a Department leads to a page listing its Courses.<br>2.4: Clicking a Course leads to a page listing all associated files for that course, grouped by category (e.g., "Exams"). |
| **FR-3**  | **File Downloading**                           | Authenticated users must be able to download any available file.                                                                                                         | 3.1: Each file listed on a course page has a "Download" button.<br>3.2: Clicking "Download" initiates a secure file download from the storage provider.<br>3.3: Unauthenticated users attempting to download are prompted to sign in first.<br>3.4: Downloads are rate-limited to 5 files per minute per user.                       |
| **FR-4**  | **Search Functionality**                       | Users must be able to search for files across the entire platform.                                                                                                       | 4.1: A search bar is present in the main application layout.<br>4.2: Entering a query and submitting returns a list of relevant files.<br>4.3: Search results must be tolerant to common typos.<br>4.4: Search results display the file name, its associated courses, and a brief description.<br>4.5: Search is rate-limited to 100 queries per hour per user. |
| **FR-5**  | **Static & Legal Pages**                       | Users must be able to access essential informational and legal pages.                                                                                                   | 5.1: The application footer contains links to "Terms of Service" and "Privacy Policy" pages.<br>5.2: These links lead to statically rendered pages containing the respective content.                                                                                                                                             |

### 2.2 Admin Panel (Retool)

| ID    | Requirement                                    | Description                                                                                                                                                              | Acceptance Criteria                                                                                                                                                                                                                                                                                                                           |
| :---- | :--------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **FR-6**  | **Content Management (CRUD)**                  | Administrators must have full control over the content hierarchy and files.                                                                                              | 6.1: Admin can create, read, update, and delete Universities, Departments, and Courses.<br>6.2: Admin can upload files and associate them with multiple courses and categories.<br>6.3: Admin can perform bulk uploads of multiple files.<br>6.4: All file metadata (name, description, academic year, tags) can be edited.            |
| **FR-7**  | **User Management**                            | Administrators must be able to manage user access.                                                                                                                       | 7.1: Admin can view a list of all registered users.<br>7.2: Admin can "suspend" or "ban" a user, preventing them from logging in.<br>7.3: Admin can see basic user information (email, sign-up date, last active date).                                                                                                                     |
| **FR-8**  | **Basic Analytics Dashboard**                  | Administrators must have a high-level overview of system usage.                                                                                                          | 8.1: The dashboard displays key metrics: total users, daily active users, total files, and total downloads.<br>8.2: A list of the top 10 most downloaded files in the last 30 days is visible.<br>8.3: Basic search analytics showing top 20 search queries with zero results.                                                        |

---

## 3. Non-Functional Requirements

This section defines the quality attributes and constraints of the system.

| ID    | Requirement          | Description                                                                                                                                                                       |
| :---- | :------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **NFR-1** | **Performance**      | The application must be fast and responsive.<br>- **P95 Latency:** Core API endpoints must respond in < 200ms.<br>- **Load Time:** Main pages (Home, Course Page) must achieve a Largest Contentful Paint (LCP) of < 2.5 seconds.<br>- **Concurrent Users:** System must handle 100 concurrent users without degradation. |
| **NFR-2** | **Security**         | The system must protect user data and prevent unauthorized access.<br>- **Data in Transit:** All network traffic must be encrypted using TLS 1.2 or higher.<br>- **Authentication:** Authentication will be handled exclusively via Google OAuth 2.0.<br>- **File Access:** File downloads must be brokered via secure, short-lived presigned URLs generated by the backend. Direct public access to the storage bucket is forbidden.<br>- **Rate Limiting:** All API endpoints must implement rate limiting to prevent abuse. |
| **NFR-3** | **Scalability**      | The architecture must handle growth efficiently.<br>- The system must be able to serve 10,000 monthly active users (MAU) without significant performance degradation.<br>- **Active User Definition:** A user who logs in and performs at least one file download or search action within a 30-day period.<br>- The backend must support horizontal scaling to handle traffic spikes during exam periods (10x normal load). |
| **NFR-4** | **Availability**     | The system must be highly available to users.<br>- Target uptime for user-facing services is 99.5% (allows ~3.5 hours downtime/month).<br>- Scheduled maintenance windows will be communicated to users in advance.<br>- **Backup & Recovery:** Daily automated database backups with 7-day retention. |
| **NFR-5** | **Usability**        | The application must be intuitive and easy to use.<br>- The user interface must be fully responsive and functional on modern mobile web browsers (Chrome on Android, Safari on iOS).<br>- The design will adhere to standard accessibility guidelines (WCAG 2.1 Level AA). |
| **NFR-6** | **Observability**    | The system's health must be transparent to the development team.<br>- All application errors (frontend and backend) must be captured and logged in a centralized error tracking service (Sentry).<br>- Key application events (user sign-ups, file downloads, search queries) must be tracked in an analytics service (PostHog).<br>- API health check endpoint must be available for uptime monitoring. |
| **NFR-7** | **Data Management**  | The system must handle data responsibly.<br>- **Data Retention:** User activity logs retained for 90 days, then aggregated.<br>- **Data Deletion:** Users can request account deletion via admin (manual process for MVP).<br>- **File Deduplication:** System must detect and prevent storage of duplicate files based on content hash. |

---

## 4. Rate Limiting Specifications

To ensure system stability and prevent abuse, the following rate limits will be enforced:

| Endpoint Type | Rate Limit | Window | Per User/IP | Action on Exceed |
| :------------ | :--------- | :----- | :---------- | :--------------- |
| **API General** | 10 requests | 1 second | Per User | 429 Too Many Requests |
| **File Downloads** | 5 downloads | 1 minute | Per User | 429 with retry-after header |
| **Search Queries** | 100 searches | 1 hour | Per User | 429 with message |
| **Authentication** | 5 attempts | 15 minutes | Per IP | Temporary IP block |

---

## 5. Constraints

This section lists the technical and procedural limitations on the project.

| ID    | Constraint         | Description                                                                                                                                                                                              |
| :---- | :----------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **C-1**   | **Tech Stack**     | The project must be built using the following approved technologies:<br>- **Frontend:** Next.js (TypeScript)<br>- **Backend:** NestJS (TypeScript)<br>- **Database:** PostgreSQL (with Prisma ORM)<br>- **Storage:** Cloudflare R2<br>- **Search:** Meilisearch<br>- **Admin Panel:** Retool |
| **C-2**   | **Platform**       | The initial product will be a web application only. No native mobile applications will be developed for the MVP.                                                                                         |
| **C-3**   | **Authentication** | User authentication is restricted to Google OAuth only. No email/password, GitHub, or other social logins will be implemented in the MVP.                                                                |
| **C-4**   | **Compliance**     | The platform must adhere to standard data privacy practices and provide a clear mechanism for users to request data deletion. It must have a published Terms of Service and Privacy Policy before launch. |
| **C-5**   | **Budget**         | The system must operate within AWS Free Tier limits during development and early operation. No paid services unless absolutely necessary.                                                                |