# 📚 Phase 5: Content & Launch Preparation (Week 5)

**Objective:** Seed content database, conduct comprehensive testing, optimize performance, and prepare for beta launch.

**Duration:** 7 days  
**Prerequisites:** Phase 4 completed, all systems operational

---

## 📋 Overview

This final phase prepares <PERSON>zan for launch:
- Seed database with 500+ academic files
- Comprehensive testing (unit, integration, e2e)
- Performance optimization and bug fixes
- Legal documentation (ToS, Privacy Policy)
- Production monitoring and alerting setup
- Beta launch with user feedback collection

**Deliverables:**
- Fully seeded content database
- Complete test suite passing
- Legal compliance documentation
- Production monitoring dashboard
- Beta user feedback system

---

## 📊 Day 1-2: Content Database Seeding

### Step 1: Prepare Content Structure

```bash
# 1. Create content organization plan
cat > content-plan.md << 'EOF'
# Mizan Content Seeding Plan

## Universities (3)
1. Addis Ababa University (AAU)
2. Jimma University
3. Hawassa University

## Departments per University (3-4)
- Computer Science
- Software Engineering  
- Information Technology
- Information Systems

## Courses per Department (8-10)
- Data Structures and Algorithms
- Database Systems
- Software Engineering
- Computer Networks
- Operating Systems
- Web Development
- Mobile Application Development
- Artificial Intelligence
- Computer Graphics
- Cybersecurity

## Files per Course (15-20)
- Past Exams: 5-7 files
- Lecture Notes: 4-6 files
- Lab Manuals: 2-3 files
- Assignments: 2-3 files
- Projects: 1-2 files

## Target: 500+ files total
EOF
```

### Step 2: Create Seeding Scripts

```bash
# 1. Create database seeding script
cat > scripts/seed-database.js << 'EOF'
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function seedUniversities() {
  const universities = [
    { name: 'Addis Ababa University', slug: 'aau' },
    { name: 'Jimma University', slug: 'ju' },
    { name: 'Hawassa University', slug: 'hu' },
  ];

  for (const uni of universities) {
    await prisma.university.upsert({
      where: { slug: uni.slug },
      update: {},
      create: uni,
    });
  }
  
  console.log('✅ Universities seeded');
}

async function seedDepartments() {
  const departments = [
    { name: 'Computer Science', slug: 'cs', universitySlug: 'aau' },
    { name: 'Software Engineering', slug: 'se', universitySlug: 'aau' },
    { name: 'Information Technology', slug: 'it', universitySlug: 'aau' },
    { name: 'Computer Science', slug: 'cs', universitySlug: 'ju' },
    { name: 'Information Technology', slug: 'it', universitySlug: 'ju' },
    { name: 'Computer Science', slug: 'cs', universitySlug: 'hu' },
    { name: 'Software Engineering', slug: 'se', universitySlug: 'hu' },
  ];

  for (const dept of departments) {
    const university = await prisma.university.findUnique({
      where: { slug: dept.universitySlug },
    });
    
    if (university) {
      await prisma.department.upsert({
        where: { 
          slug_universityId: { 
            slug: dept.slug, 
            universityId: university.id 
          } 
        },
        update: {},
        create: {
          name: dept.name,
          slug: dept.slug,
          universityId: university.id,
        },
      });
    }
  }
  
  console.log('✅ Departments seeded');
}

async function seedCourses() {
  const courses = [
    { name: 'Data Structures and Algorithms', code: 'CS-301', slug: 'dsa' },
    { name: 'Database Systems', code: 'CS-302', slug: 'db-systems' },
    { name: 'Software Engineering', code: 'SE-401', slug: 'software-eng' },
    { name: 'Computer Networks', code: 'CS-303', slug: 'networks' },
    { name: 'Operating Systems', code: 'CS-304', slug: 'os' },
    { name: 'Web Development', code: 'CS-305', slug: 'web-dev' },
    { name: 'Mobile App Development', code: 'CS-306', slug: 'mobile-dev' },
    { name: 'Artificial Intelligence', code: 'CS-401', slug: 'ai' },
    { name: 'Computer Graphics', code: 'CS-402', slug: 'graphics' },
    { name: 'Cybersecurity', code: 'CS-403', slug: 'cybersecurity' },
  ];

  const departments = await prisma.department.findMany();
  
  for (const dept of departments) {
    for (const course of courses) {
      await prisma.course.upsert({
        where: { 
          courseCode_departmentId: { 
            courseCode: course.code, 
            departmentId: dept.id 
          } 
        },
        update: {},
        create: {
          name: course.name,
          courseCode: course.code,
          slug: course.slug,
          departmentId: dept.id,
        },
      });
    }
  }
  
  console.log('✅ Courses seeded');
}

async function main() {
  try {
    await seedUniversities();
    await seedDepartments();
    await seedCourses();
    console.log('🎉 Database seeding completed!');
  } catch (error) {
    console.error('❌ Seeding failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
EOF

# 2. Run seeding script
node scripts/seed-database.js
```

### Step 3: Bulk File Upload Process

```bash
# 1. Create file upload script for admin
cat > scripts/bulk-upload.js << 'EOF'
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');

const API_BASE = 'http://localhost:3000/api';
const ADMIN_TOKEN = 'your-admin-jwt-token'; // Get from login

async function uploadFile(filePath, metadata) {
  try {
    // 1. Get upload URL
    const uploadUrlResponse = await axios.post(
      `${API_BASE}/admin/files/upload-url`,
      {
        fileName: path.basename(filePath),
        fileType: 'application/pdf', // Adjust based on file type
      },
      {
        headers: { Authorization: `Bearer ${ADMIN_TOKEN}` },
      }
    );

    const { uploadUrl, fileKey } = uploadUrlResponse.data;

    // 2. Upload file to R2
    const fileBuffer = fs.readFileSync(filePath);
    const uploadResponse = await axios.put(uploadUrl, fileBuffer, {
      headers: { 'Content-Type': 'application/pdf' },
    });

    if (uploadResponse.status === 200) {
      // 3. Create file record
      const fileRecord = await axios.post(
        `${API_BASE}/admin/files`,
        {
          ...metadata,
          fileName: path.basename(filePath),
          originalName: path.basename(filePath),
          fileType: 'application/pdf',
          fileSize: fileBuffer.length,
          storageKey: fileKey,
        },
        {
          headers: { Authorization: `Bearer ${ADMIN_TOKEN}` },
        }
      );

      console.log(`✅ Uploaded: ${path.basename(filePath)}`);
      return fileRecord.data;
    }
  } catch (error) {
    console.error(`❌ Failed to upload ${filePath}:`, error.message);
  }
}

async function bulkUpload() {
  // Define file metadata templates
  const fileTemplates = [
    {
      title: 'Data Structures Midterm Exam 2024',
      description: 'Midterm examination covering arrays, linked lists, and trees',
      academicYear: 2024,
      semester: 1,
      category: 'EXAM',
      courseIds: [], // Will be populated with actual course IDs
    },
    // Add more templates...
  ];

  // Get course IDs for association
  const coursesResponse = await axios.get(`${API_BASE}/courses`, {
    headers: { Authorization: `Bearer ${ADMIN_TOKEN}` },
  });
  
  // Upload files (you'll need to have actual PDF files)
  const filesDir = './sample-files';
  if (fs.existsSync(filesDir)) {
    const files = fs.readdirSync(filesDir);
    
    for (const file of files) {
      if (file.endsWith('.pdf')) {
        const filePath = path.join(filesDir, file);
        const metadata = fileTemplates[0]; // Use appropriate template
        await uploadFile(filePath, metadata);
      }
    }
  }
}

bulkUpload();
EOF
```

### Step 4: Generate Sample Content

```bash
# 1. Create sample file generator (for testing)
cat > scripts/generate-sample-files.js << 'EOF'
const fs = require('fs');
const path = require('path');
const PDFDocument = require('pdfkit');

function generateSamplePDF(title, content, outputPath) {
  const doc = new PDFDocument();
  doc.pipe(fs.createWriteStream(outputPath));
  
  doc.fontSize(20).text(title, 100, 100);
  doc.fontSize(12).text(content, 100, 150);
  
  doc.end();
}

async function generateSampleFiles() {
  const sampleDir = './sample-files';
  if (!fs.existsSync(sampleDir)) {
    fs.mkdirSync(sampleDir);
  }

  const fileTypes = [
    { type: 'exam', count: 100 },
    { type: 'lecture', count: 150 },
    { type: 'lab', count: 100 },
    { type: 'assignment', count: 100 },
    { type: 'project', count: 50 },
  ];

  for (const fileType of fileTypes) {
    for (let i = 1; i <= fileType.count; i++) {
      const fileName = `${fileType.type}-${i.toString().padStart(3, '0')}.pdf`;
      const filePath = path.join(sampleDir, fileName);
      
      const title = `Sample ${fileType.type.charAt(0).toUpperCase() + fileType.type.slice(1)} ${i}`;
      const content = `This is a sample ${fileType.type} document for testing purposes. Generated automatically for Mizan platform.`;
      
      generateSamplePDF(title, content, filePath);
    }
  }
  
  console.log('✅ Sample files generated');
}

generateSampleFiles();
EOF

# 2. Install required packages and generate files
npm install pdfkit
node scripts/generate-sample-files.js
```

---

## 🧪 Day 2-3: Comprehensive Testing Suite

### Step 1: Backend Unit Tests

```bash
# 1. Create comprehensive test suite
cat > src/auth/auth.service.spec.ts << 'EOF'
import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UserService } from '../user/user.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

describe('AuthService', () => {
  let service: AuthService;
  let userService: UserService;
  let jwtService: JwtService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UserService,
          useValue: {
            findByGoogleId: jest.fn(),
            create: jest.fn(),
            updateLoginInfo: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userService = module.get<UserService>(UserService);
    jwtService = module.get<JwtService>(JwtService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateGoogleUser', () => {
    it('should create new user if not exists', async () => {
      const googleUser = {
        googleId: '123',
        email: '<EMAIL>',
        name: 'Test User',
        avatarUrl: 'https://example.com/avatar.jpg',
      };

      jest.spyOn(userService, 'findByGoogleId').mockResolvedValue(null);
      jest.spyOn(userService, 'create').mockResolvedValue({
        id: 'user-id',
        ...googleUser,
        role: 'USER',
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLoginAt: new Date(),
        loginCount: 1,
        deletedAt: null,
      });

      const result = await service.validateGoogleUser(googleUser);

      expect(userService.findByGoogleId).toHaveBeenCalledWith('123');
      expect(userService.create).toHaveBeenCalledWith(googleUser);
      expect(result).toBeDefined();
    });

    it('should update existing user login info', async () => {
      const existingUser = {
        id: 'user-id',
        googleId: '123',
        email: '<EMAIL>',
        name: 'Test User',
        avatarUrl: 'https://example.com/avatar.jpg',
        role: 'USER' as const,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLoginAt: new Date(),
        loginCount: 5,
        deletedAt: null,
      };

      jest.spyOn(userService, 'findByGoogleId').mockResolvedValue(existingUser);
      jest.spyOn(userService, 'updateLoginInfo').mockResolvedValue({
        ...existingUser,
        loginCount: 6,
        lastLoginAt: new Date(),
      });

      const result = await service.validateGoogleUser({
        googleId: '123',
        email: '<EMAIL>',
        name: 'Test User',
        avatarUrl: 'https://example.com/avatar.jpg',
      });

      expect(userService.findByGoogleId).toHaveBeenCalledWith('123');
      expect(userService.updateLoginInfo).toHaveBeenCalledWith('user-id');
      expect(result).toBeDefined();
    });
  });
});
EOF

# 2. Run unit tests
npm test

# 3. Generate test coverage report
npm run test:cov
```

### Step 2: Integration Tests

```bash
# 1. Create API integration tests
cat > test/app.e2e-spec.ts << 'EOF'
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from './../src/app.module';
import { PrismaService } from '../src/prisma.service';

describe('AppController (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    prisma = moduleFixture.get<PrismaService>(PrismaService);
    
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/health (GET)', () => {
    it('should return health status', () => {
      return request(app.getHttpServer())
        .get('/api/health')
        .expect(200)
        .expect((res) => {
          expect(res.body.status).toBe('ok');
          expect(res.body.timestamp).toBeDefined();
        });
    });
  });

  describe('/universities (GET)', () => {
    it('should return universities list', async () => {
      // Seed test data
      await prisma.university.create({
        data: {
          name: 'Test University',
          slug: 'test-uni',
        },
      });

      return request(app.getHttpServer())
        .get('/api/universities')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
          expect(res.body.length).toBeGreaterThan(0);
        });
    });
  });

  describe('/search (GET)', () => {
    it('should require authentication', () => {
      return request(app.getHttpServer())
        .get('/api/search?q=test')
        .expect(401);
    });

    it('should return search results with valid token', async () => {
      // This would require setting up authentication in tests
      // For now, we'll skip the actual implementation
      expect(true).toBe(true);
    });
  });
});
EOF

# 2. Run integration tests
npm run test:e2e
```

### Step 3: Frontend Testing

```bash
# 1. Create component tests
cat > src/components/auth/sign-in-button.test.tsx << 'EOF'
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SignInButton } from './sign-in-button';
import { signIn } from '@/lib/auth-client';

// Mock the auth client
jest.mock('@/lib/auth-client', () => ({
  signIn: {
    social: jest.fn(),
  },
}));

describe('SignInButton', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders sign in button', () => {
    render(<SignInButton />);
    expect(screen.getByText('Sign in with Google')).toBeInTheDocument();
  });

  it('shows loading state when signing in', async () => {
    const mockSignIn = signIn.social as jest.MockedFunction<typeof signIn.social>;
    mockSignIn.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

    render(<SignInButton />);
    
    fireEvent.click(screen.getByText('Sign in with Google'));
    
    expect(screen.getByText('Signing in...')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Sign in with Google')).toBeInTheDocument();
    });
  });

  it('handles sign in error', async () => {
    const mockSignIn = signIn.social as jest.MockedFunction<typeof signIn.social>;
    mockSignIn.mockRejectedValue(new Error('Sign in failed'));

    render(<SignInButton />);
    
    fireEvent.click(screen.getByText('Sign in with Google'));
    
    await waitFor(() => {
      expect(screen.getByText('Sign in with Google')).toBeInTheDocument();
    });
  });
});
EOF

# 2. Run frontend tests
cd ~/mizan/frontend/mizan-frontend
npm test
```

---

## ⚡ Day 3-4: Performance Optimization

### Step 1: Database Optimization

```bash
# 1. Add database indexes for performance
cat > prisma/migrations/add_performance_indexes.sql << 'EOF'
-- Add indexes for frequently queried fields
CREATE INDEX IF NOT EXISTS idx_files_category_year_semester ON "File" ("category", "academicYear", "semester");
CREATE INDEX IF NOT EXISTS idx_files_created_at_desc ON "File" ("createdAt" DESC);
CREATE INDEX IF NOT EXISTS idx_files_download_count_desc ON "File" ("downloadCount" DESC);
CREATE INDEX IF NOT EXISTS idx_search_queries_created_at ON "SearchQuery" ("createdAt");
CREATE INDEX IF NOT EXISTS idx_downloads_created_at ON "Download" ("createdAt");

-- Add composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_file_course_primary ON "FileCourse" ("courseId", "isPrimary");
CREATE INDEX IF NOT EXISTS idx_user_email_deleted ON "User" ("email") WHERE "deletedAt" IS NULL;
EOF

# 2. Apply indexes
npx prisma db execute --file prisma/migrations/add_performance_indexes.sql
```

### Step 2: API Response Optimization

```bash
# 1. Add response caching
cat > src/common/interceptors/cache.interceptor.ts << 'EOF'
import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private cache = new Map();

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const cacheKey = `${request.method}:${request.url}`;
    
    // Only cache GET requests
    if (request.method !== 'GET') {
      return next.handle();
    }

    // Check if response is cached
    if (this.cache.has(cacheKey)) {
      const cachedResponse = this.cache.get(cacheKey);
      if (Date.now() - cachedResponse.timestamp < 300000) { // 5 minutes
        return new Observable(observer => {
          observer.next(cachedResponse.data);
          observer.complete();
        });
      }
    }

    return next.handle().pipe(
      tap(response => {
        // Cache the response
        this.cache.set(cacheKey, {
          data: response,
          timestamp: Date.now(),
        });
      }),
    );
  }
}
EOF

# 2. Optimize database queries
cat > src/hierarchy/hierarchy.service.ts << 'EOF'
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';

@Injectable()
export class HierarchyService {
  constructor(private prisma: PrismaService) {}

  async getUniversities() {
    return this.prisma.university.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        _count: {
          select: { departments: true },
        },
      },
      orderBy: { name: 'asc' },
    });
  }

  async getDepartmentsByUniversity(universitySlug: string) {
    const university = await this.prisma.university.findUnique({
      where: { slug: universitySlug },
      select: {
        id: true,
        name: true,
        departments: {
          select: {
            id: true,
            name: true,
            slug: true,
            _count: {
              select: { courses: true },
            },
          },
          orderBy: { name: 'asc' },
        },
      },
    });

    if (!university) {
      throw new Error('University not found');
    }

    return university.departments;
  }

  // Optimized with proper select fields
  async getCoursesByDepartment(departmentSlug: string, universitySlug: string) {
    const department = await this.prisma.department.findFirst({
      where: {
        slug: departmentSlug,
        university: { slug: universitySlug },
      },
      select: {
        id: true,
        name: true,
        courses: {
          select: {
            id: true,
            name: true,
            courseCode: true,
            slug: true,
            _count: {
              select: { files: true },
            },
          },
          orderBy: { name: 'asc' },
        },
      },
    });

    if (!department) {
      throw new Error('Department not found');
    }

    return department.courses;
  }
}
EOF
```

### Step 3: Frontend Performance

```bash
# 1. Optimize bundle size
cd ~/mizan/frontend/mizan-frontend

# 2. Analyze bundle
npm install -D @next/bundle-analyzer
npm run build
npm run analyze

# 3. Add performance monitoring
cat > src/lib/performance.ts << 'EOF'
export function measurePerformance(name: string, fn: () => Promise<any>) {
  return async (...args: any[]) => {
    const start = performance.now();
    try {
      const result = await fn.apply(this, args);
      const end = performance.now();
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`⚡ ${name} took ${(end - start).toFixed(2)}ms`);
      }
      
      return result;
    } catch (error) {
      const end = performance.now();
      console.error(`❌ ${name} failed after ${(end - start).toFixed(2)}ms:`, error);
      throw error;
    }
  };
}

export function reportWebVitals(metric: any) {
  if (process.env.NODE_ENV === 'production') {
    // Send to analytics service
    console.log(metric);
  }
}
EOF
```

---

## 📄 Day 4-5: Legal Documentation

### Step 1: Terms of Service

```bash
# 1. Create Terms of Service
cat > src/app/terms/page.tsx << 'EOF'
import { MainLayout } from '@/components/layout/main-layout';

export default function TermsOfService() {
  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Terms of Service</h1>
        
        <div className="prose prose-lg max-w-none">
          <p className="text-gray-600 mb-6">
            <strong>Last updated:</strong> {new Date().toLocaleDateString()}
          </p>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">1. Acceptance of Terms</h2>
            <p className="text-gray-700 mb-4">
              By accessing and using Mizan ("the Service"), you accept and agree to be bound by the terms and provision of this agreement.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">2. Description of Service</h2>
            <p className="text-gray-700 mb-4">
              Mizan is an academic document sharing platform that allows users to access and download educational materials including past exams, lecture notes, and study resources.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">3. User Responsibilities</h2>
            <ul className="list-disc pl-6 text-gray-700 space-y-2">
              <li>You must provide accurate information when creating an account</li>
              <li>You are responsible for maintaining the confidentiality of your account</li>
              <li>You agree to use the Service only for lawful purposes</li>
              <li>You will not attempt to gain unauthorized access to the Service</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">4. Content Usage</h2>
            <p className="text-gray-700 mb-4">
              The documents available on Mizan are provided for educational purposes only. Users are responsible for ensuring their use of downloaded materials complies with applicable copyright laws and institutional policies.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">5. Privacy</h2>
            <p className="text-gray-700 mb-4">
              Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">6. Limitation of Liability</h2>
            <p className="text-gray-700 mb-4">
              Mizan shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of the Service.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">7. Changes to Terms</h2>
            <p className="text-gray-700 mb-4">
              We reserve the right to modify these terms at any time. Users will be notified of significant changes via email or through the Service.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">8. Contact Information</h2>
            <p className="text-gray-700 mb-4">
              If you have any questions about these Terms of Service, please contact <NAME_EMAIL>
            </p>
          </section>
        </div>
      </div>
    </MainLayout>
  );
}
EOF
```

### Step 2: Privacy Policy

```bash
# 1. Create Privacy Policy
cat > src/app/privacy/page.tsx << 'EOF'
import { MainLayout } from '@/components/layout/main-layout';

export default function PrivacyPolicy() {
  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Privacy Policy</h1>
        
        <div className="prose prose-lg max-w-none">
          <p className="text-gray-600 mb-6">
            <strong>Last updated:</strong> {new Date().toLocaleDateString()}
          </p>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">1. Information We Collect</h2>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Personal Information</h3>
            <ul className="list-disc pl-6 text-gray-700 space-y-2 mb-4">
              <li>Name and email address (via Google OAuth)</li>
              <li>Profile picture (via Google OAuth)</li>
              <li>Usage data and analytics</li>
            </ul>
            
            <h3 className="text-lg font-medium text-gray-900 mb-2">Usage Information</h3>
            <ul className="list-disc pl-6 text-gray-700 space-y-2">
              <li>Files downloaded and search queries</li>
              <li>Pages visited and time spent on the platform</li>
              <li>Device and browser information</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">2. How We Use Your Information</h2>
            <ul className="list-disc pl-6 text-gray-700 space-y-2">
              <li>To provide and maintain our Service</li>
              <li>To authenticate and authorize users</li>
              <li>To improve our Service and user experience</li>
              <li>To analyze usage patterns and trends</li>
              <li>To communicate with users about the Service</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">3. Information Sharing</h2>
            <p className="text-gray-700 mb-4">
              We do not sell, trade, or otherwise transfer your personal information to third parties except as described in this policy:
            </p>
            <ul className="list-disc pl-6 text-gray-700 space-y-2">
              <li>With your consent</li>
              <li>To comply with legal obligations</li>
              <li>To protect our rights and safety</li>
              <li>With service providers who assist in operating our platform</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">4. Data Security</h2>
            <p className="text-gray-700 mb-4">
              We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">5. Data Retention</h2>
            <p className="text-gray-700 mb-4">
              We retain your personal information for as long as necessary to provide our services and comply with legal obligations. User activity logs are retained for 90 days, then aggregated for analytics.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">6. Your Rights</h2>
            <p className="text-gray-700 mb-4">You have the right to:</p>
            <ul className="list-disc pl-6 text-gray-700 space-y-2">
              <li>Access your personal information</li>
              <li>Correct inaccurate information</li>
              <li>Request deletion of your account and data</li>
              <li>Object to processing of your information</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">7. Third-Party Services</h2>
            <p className="text-gray-700 mb-4">
              Our Service uses Google OAuth for authentication. Please review Google's Privacy Policy for information about how Google handles your data.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">8. Contact Us</h2>
            <p className="text-gray-700 mb-4">
              If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>
            </p>
          </section>
        </div>
      </div>
    </MainLayout>
  );
}
EOF
```

---

## 📊 Day 5-6: Production Monitoring Setup

### Step 1: Uptime Monitoring

```bash
# 1. Setup UptimeRobot (Free tier)
# Visit: https://uptimerobot.com/
# Create monitors for:
# - http://<EC2_PUBLIC_IP>/health
# - http://<EC2_PUBLIC_IP>:7700/health
# - https://your-frontend.vercel.app

# 2. Create monitoring dashboard
cat > monitoring-setup.md << 'EOF'
# Mizan Monitoring Setup

## UptimeRobot Monitors
1. Backend API Health: http://<EC2_PUBLIC_IP>/health
2. Meilisearch Health: http://<EC2_PUBLIC_IP>:7700/health  
3. Frontend: https://your-frontend.vercel.app

## Alert Contacts
- Email: <EMAIL>
- Slack: #mizan-alerts (optional)

## Check Intervals
- Every 5 minutes for critical services
- Every 15 minutes for secondary services
EOF
```

### Step 2: Application Monitoring

```bash
# 1. Create monitoring dashboard endpoint
cat > src/admin/monitoring.controller.ts << 'EOF'
import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AdminGuard } from '../auth/admin.guard';
import { PrismaService } from '../prisma.service';
import { MeilisearchService } from '../search/meilisearch.service';

@Controller('admin/monitoring')
@UseGuards(JwtAuthGuard, AdminGuard)
export class MonitoringController {
  constructor(
    private prisma: PrismaService,
    private meilisearch: MeilisearchService,
  ) {}

  @Get('dashboard')
  async getDashboard() {
    const [
      totalUsers,
      totalFiles,
      totalDownloads,
      recentSearches,
      topFiles,
      searchStats,
    ] = await Promise.all([
      this.prisma.user.count({ where: { deletedAt: null } }),
      this.prisma.file.count({ where: { deletedAt: null } }),
      this.prisma.download.count(),
      this.prisma.searchQuery.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        select: { query: true, resultsCount: true, createdAt: true },
      }),
      this.prisma.file.findMany({
        take: 10,
        orderBy: { downloadCount: 'desc' },
        select: {
          id: true,
          title: true,
          downloadCount: true,
          viewCount: true,
        },
      }),
      this.meilisearch.getStats(),
    ]);

    return {
      overview: {
        totalUsers,
        totalFiles,
        totalDownloads,
        searchIndexSize: searchStats.numberOfDocuments,
      },
      recentActivity: {
        recentSearches,
        topFiles,
      },
      systemHealth: {
        database: 'healthy',
        search: searchStats.isIndexing ? 'indexing' : 'healthy',
        storage: 'healthy',
      },
    };
  }

  @Get('metrics')
  async getMetrics() {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const [
      dailyUsers,
      dailyDownloads,
      dailySearches,
      weeklyUsers,
      weeklyDownloads,
    ] = await Promise.all([
      this.prisma.user.count({
        where: {
          lastLoginAt: { gte: yesterday },
          deletedAt: null,
        },
      }),
      this.prisma.download.count({
        where: { createdAt: { gte: yesterday } },
      }),
      this.prisma.searchQuery.count({
        where: { createdAt: { gte: yesterday } },
      }),
      this.prisma.user.count({
        where: {
          lastLoginAt: { gte: lastWeek },
          deletedAt: null,
        },
      }),
      this.prisma.download.count({
        where: { createdAt: { gte: lastWeek } },
      }),
    ]);

    return {
      daily: {
        activeUsers: dailyUsers,
        downloads: dailyDownloads,
        searches: dailySearches,
      },
      weekly: {
        activeUsers: weeklyUsers,
        downloads: weeklyDownloads,
      },
    };
  }
}
EOF
```

---

## 🚀 Day 6-7: Beta Launch and Feedback

### Step 1: Pre-Launch Checklist

```bash
# 1. Create comprehensive pre-launch checklist
cat > pre-launch-checklist.md << 'EOF'
# Mizan Pre-Launch Checklist

## Infrastructure ✅
- [ ] All services running and healthy
- [ ] SSL certificates configured
- [ ] Backup systems tested
- [ ] Monitoring and alerting active
- [ ] Performance benchmarks met

## Content ✅
- [ ] 500+ files uploaded and indexed
- [ ] All universities, departments, courses seeded
- [ ] Search index populated and working
- [ ] File downloads tested
- [ ] Content quality reviewed

## Security ✅
- [ ] Rate limiting active on all endpoints
- [ ] Authentication working correctly
- [ ] Input validation implemented
- [ ] Security headers configured
- [ ] CORS properly configured

## Legal ✅
- [ ] Terms of Service published
- [ ] Privacy Policy published
- [ ] Contact information available
- [ ] Data retention policies implemented

## Testing ✅
- [ ] Unit tests passing
- [ ] Integration tests passing
- [ ] Manual testing completed
- [ ] Performance testing done
- [ ] Security testing completed

## Monitoring ✅
- [ ] Error tracking active (Sentry)
- [ ] Analytics tracking events (PostHog)
- [ ] Uptime monitoring configured
- [ ] Performance monitoring active
- [ ] Log aggregation working
EOF

# 2. Run final system check
./scripts/system-check.sh
```

### Step 2: Beta User Onboarding

```bash
# 1. Create beta user invitation system
cat > scripts/invite-beta-users.js << 'EOF'
const nodemailer = require('nodemailer');

const transporter = nodemailer.createTransporter({
  // Configure your email service
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'your-app-password',
  },
});

const betaUsers = [
  { name: 'John Doe', email: '<EMAIL>', university: 'AAU' },
  { name: 'Jane Smith', email: '<EMAIL>', university: 'JU' },
  // Add more beta users
];

async function sendBetaInvitations() {
  for (const user of betaUsers) {
    const mailOptions = {
      from: '<EMAIL>',
      to: user.email,
      subject: 'Welcome to Mizan Beta!',
      html: `
        <h2>Welcome to Mizan Beta, ${user.name}!</h2>
        <p>You've been invited to test our new academic document sharing platform.</p>
        
        <h3>What is Mizan?</h3>
        <p>Mizan is a platform where ${user.university} students can find and download academic materials including past exams, lecture notes, and study resources.</p>
        
        <h3>Getting Started</h3>
        <ol>
          <li>Visit <a href="https://your-frontend.vercel.app">Mizan</a></li>
          <li>Sign in with your Google account</li>
          <li>Browse universities, departments, and courses</li>
          <li>Search for and download files</li>
        </ol>
        
        <h3>We Need Your Feedback</h3>
        <p>As a beta user, your feedback is crucial. Please let us know:</p>
        <ul>
          <li>What works well?</li>
          <li>What could be improved?</li>
          <li>What features are missing?</li>
          <li>Any bugs or issues you encounter</li>
        </ul>
        
        <p>Send feedback to: <EMAIL></p>
        
        <p>Thank you for helping us improve Mizan!</p>
        <p>The Mizan Team</p>
      `,
    };

    try {
      await transporter.sendMail(mailOptions);
      console.log(`✅ Invitation sent to ${user.email}`);
    } catch (error) {
      console.error(`❌ Failed to send invitation to ${user.email}:`, error);
    }
  }
}

sendBetaInvitations();
EOF
```

### Step 3: Feedback Collection System

```bash
# 1. Create feedback collection endpoint
cat > src/feedback/feedback.controller.ts << 'EOF'
import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { PrismaService } from '../prisma.service';
import { IsString, IsEmail, IsOptional, MaxLength } from 'class-validator';

class FeedbackDto {
  @IsString()
  @MaxLength(100)
  subject: string;

  @IsString()
  @MaxLength(1000)
  message: string;

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsEmail()
  email?: string;
}

@Controller('feedback')
@UseGuards(JwtAuthGuard)
export class FeedbackController {
  constructor(private prisma: PrismaService) {}

  @Post()
  async submitFeedback(@Body() feedbackDto: FeedbackDto) {
    // Store feedback in database
    const feedback = await this.prisma.feedback.create({
      data: {
        subject: feedbackDto.subject,
        message: feedbackDto.message,
        category: feedbackDto.category || 'general',
        email: feedbackDto.email,
        createdAt: new Date(),
      },
    });

    // Send notification email to team (optional)
    // await this.notificationService.sendFeedbackNotification(feedback);

    return {
      message: 'Feedback submitted successfully',
      id: feedback.id,
    };
  }
}
EOF

# 2. Add feedback model to Prisma schema
cat >> prisma/schema.prisma << 'EOF'

model Feedback {
  id        String   @id @default(uuid())
  subject   String
  message   String
  category  String   @default("general")
  email     String?
  createdAt DateTime @default(now())
  
  @@index([createdAt])
}
EOF

# 3. Apply schema changes
npx prisma db push
```

---

## ✅ Phase 5 Completion Checklist

### Content ✅
- [ ] 500+ files uploaded with proper metadata
- [ ] All hierarchy levels seeded (universities, departments, courses)
- [ ] Search index populated and returning results
- [ ] File downloads working correctly
- [ ] Content quality reviewed and approved

### Testing ✅
- [ ] Unit test suite passing (>80% coverage)
- [ ] Integration tests covering all API endpoints
- [ ] Frontend component tests working
- [ ] Manual testing completed across all features
- [ ] Performance benchmarks met

### Legal & Compliance ✅
- [ ] Terms of Service published and accessible
- [ ] Privacy Policy published and accessible
- [ ] Contact information available
- [ ] Data retention policies implemented
- [ ] GDPR compliance measures in place

### Monitoring ✅
- [ ] Uptime monitoring configured (UptimeRobot)
- [ ] Error tracking active (Sentry)
- [ ] Analytics tracking events (PostHog)
- [ ] Performance monitoring dashboard
- [ ] Log aggregation and alerting

### Launch Preparation ✅
- [ ] Beta user list prepared (20+ users)
- [ ] Invitation system ready
- [ ] Feedback collection system implemented
- [ ] Support documentation created
- [ ] Launch announcement prepared

### System Health ✅
- [ ] All services running and healthy
- [ ] Database performance optimized
- [ ] API response times < 200ms
- [ ] Frontend load times < 2.5s
- [ ] Search response times < 100ms

---

## 🎉 Launch Day Activities

```bash
# 1. Final system check
curl http://<EC2_PUBLIC_IP>/health
curl http://<EC2_PUBLIC_IP>:7700/health
curl https://your-frontend.vercel.app

# 2. Send beta invitations
node scripts/invite-beta-users.js

# 3. Monitor system metrics
# Check Sentry dashboard
# Check PostHog events
# Check UptimeRobot status

# 4. Collect initial feedback
# Monitor <EMAIL>
# Check feedback API endpoint
# Review user behavior analytics
```

---

## 🚀 Post-Launch Roadmap

### Week 1: Stabilization
- Monitor system performance and user feedback
- Fix critical bugs and performance issues
- Optimize search relevance based on usage patterns
- Implement user-requested quick fixes

### Week 2-4: Enhancement
- Add advanced search filters
- Implement file preview functionality
- Add user favorites and bookmarks
- Improve mobile experience
- Add more universities and content

### Month 2-3: Growth
- Implement user contribution system
- Add social features (ratings, comments)
- Optimize for SEO and discoverability
- Add email notifications
- Implement advanced analytics

---

## 🎯 Success Metrics

**Week 1 Targets:**
- 50+ active beta users
- 1000+ file downloads
- 500+ search queries
- <5% error rate
- >99% uptime

**Month 1 Targets:**
- 200+ registered users
- 5000+ file downloads
- 2000+ search queries
- Positive user feedback (>4/5 rating)
- Feature requests prioritized

---

**🎉 Congratulations! Mizan MVP is now live and ready for users!**

Your academic document sharing platform is complete with:
- ✅ 500+ seeded academic files
- ✅ Fast, intelligent search capabilities
- ✅ Comprehensive admin panel
- ✅ Production monitoring and analytics
- ✅ Legal compliance and security
- ✅ Beta user feedback system

**Platform URLs:**
- **Frontend:** https://your-frontend.vercel.app
- **API:** http://<EC2_PUBLIC_IP>/api
- **Admin Panel:** Your Retool app URL
- **Monitoring:** Your monitoring dashboards
